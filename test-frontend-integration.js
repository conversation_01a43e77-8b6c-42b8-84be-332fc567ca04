// Test frontend-backend integration
const testFrontendIntegration = async () => {
    console.log('🔗 Testing Frontend-Backend Integration...\n');
    
    // Test 1: API Base URL Resolution
    console.log('1. Testing API Base URL Resolution...');
    const apiUrls = [
        'http://localhost:3001/api',  // Development
        'https://www.sonalibd.org/api'  // Production
    ];
    
    for (const baseUrl of apiUrls) {
        try {
            const response = await fetch(`${baseUrl}/health`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });
            
            if (response.ok) {
                console.log(`✅ ${baseUrl}: Accessible`);
            } else {
                console.log(`❌ ${baseUrl}: Not accessible (${response.status})`);
            }
        } catch (error) {
            console.log(`❌ ${baseUrl}: Connection failed - ${error.message}`);
        }
    }
    
    console.log('');
    
    // Test 2: Login API with different response formats
    console.log('2. Testing Login API Response Format...');
    
    try {
        const response = await fetch('http://localhost:3001/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                identifier: 'ADMIN001',
                password: 'admin123',
                rememberMe: false
            })
        });
        
        const data = await response.json();
        
        console.log('Response structure analysis:');
        console.log(`- success: ${data.success}`);
        console.log(`- message: ${data.message}`);
        console.log(`- has data property: ${!!data.data}`);
        console.log(`- has user property: ${!!data.user}`);
        console.log(`- has accessToken property: ${!!data.accessToken}`);
        console.log(`- has sessionId property: ${!!data.sessionId}`);
        console.log(`- has expiresAt property: ${!!data.expiresAt}`);
        
        // Test frontend auth service compatibility
        console.log('\n3. Testing Frontend Auth Service Compatibility...');
        
        if (data.success) {
            // Simulate frontend auth service logic
            const loginData = data.data || {
                user: data.user,
                accessToken: data.accessToken,
                sessionId: data.sessionId || 'session-' + Date.now(),
                expiresAt: data.expiresAt
            };
            
            if (loginData.user && loginData.accessToken) {
                console.log('✅ Frontend auth service compatibility: PASS');
                console.log(`   User: ${loginData.user.name}`);
                console.log(`   Role: ${loginData.user.role}`);
                console.log(`   Token: ${loginData.accessToken.substring(0, 20)}...`);
                console.log(`   Session ID: ${loginData.sessionId}`);
            } else {
                console.log('❌ Frontend auth service compatibility: FAIL');
                console.log('   Missing required fields');
            }
        }
        
    } catch (error) {
        console.log(`❌ Login API test failed: ${error.message}`);
    }
    
    console.log('');
    
    // Test 3: CORS Configuration
    console.log('4. Testing CORS Configuration...');
    
    try {
        const response = await fetch('http://localhost:3001/api/auth/login', {
            method: 'OPTIONS',
            headers: {
                'Origin': 'http://localhost:5173',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            }
        });
        
        const corsHeaders = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
            'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
        };
        
        console.log('CORS Headers:');
        Object.entries(corsHeaders).forEach(([key, value]) => {
            console.log(`   ${key}: ${value || 'Not set'}`);
        });
        
        if (corsHeaders['Access-Control-Allow-Origin']) {
            console.log('✅ CORS: Configured');
        } else {
            console.log('❌ CORS: Not properly configured');
        }
        
    } catch (error) {
        console.log(`❌ CORS test failed: ${error.message}`);
    }
    
    console.log('');
    
    // Test 4: Environment Configuration
    console.log('5. Testing Environment Configuration...');
    
    const envTests = [
        { name: 'Development API URL', url: 'http://localhost:3001/api' },
        { name: 'Production API URL', url: 'https://www.sonalibd.org/api' }
    ];
    
    for (const test of envTests) {
        try {
            const response = await fetch(`${test.url}/health`, {
                method: 'GET',
                timeout: 5000
            });
            
            if (response.ok) {
                console.log(`✅ ${test.name}: Available`);
            } else {
                console.log(`❌ ${test.name}: Not available (${response.status})`);
            }
        } catch (error) {
            console.log(`❌ ${test.name}: ${error.message}`);
        }
    }
    
    console.log('\n🎯 Frontend-Backend Integration Test Complete!');
};

testFrontendIntegration().catch(console.error);
