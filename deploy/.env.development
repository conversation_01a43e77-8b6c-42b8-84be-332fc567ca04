# Development Environment Configuration for Sonali App (No Docker)

# Application
NODE_ENV=development
PORT=3001
APP_NAME=sonali-app-dev

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sonali_app_development
DB_USER=sonali_user
DB_PASSWORD=fe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8
DATABASE_URL=mysql://sonali_user:fe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8@localhost:3306/sonali_app_development

# JWT Configuration
JWT_SECRET=development-jwt-secret-key-not-for-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=development-refresh-secret-key-not-for-production
JWT_REFRESH_EXPIRES_IN=7d

# Security
BCRYPT_SALT_ROUNDS=10
SESSION_SECRET=development-session-secret-key

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
FRONTEND_URL=http://localhost:5173
API_URL=http://localhost:3001/api

# Domain Configuration
DOMAIN=localhost

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/var/www/sonali-app/uploads

# Rate Limiting (more lenient for development)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
LOGIN_RATE_LIMIT_MAX=50

# Logging
LOG_LEVEL=debug
LOG_FILE=/var/www/sonali-app/logs/app-dev.log

# Development specific
DEBUG=true
ENABLE_SWAGGER=true
