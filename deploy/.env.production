# Production Environment Configuration for Sonali App (No Docker)

# Application
NODE_ENV=production
PORT=3001
APP_NAME=sonali-app

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sonali_app_production
DB_USER=sonali_user
DB_PASSWORD=fe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8
DATABASE_URL=mysql://sonali_user:fe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8@localhost:3306/sonali_app_production

# JWT Configuration
JWT_SECRET=0c9ba451eb176b76e986d00a1ebbb20a190d872db2ffd698a22df85763a8e0f3112e816606eb7a6e5ce11d185f90c18277ffcc2f13c09aeadaf8ec9fda026037
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=2c04407813ebe4c225164845c65c797fd85fcc894328909372656a3e26ad7b6c307c6d8ecb30a950a33c97b0c317678d2aa59be13acd9b8f26015b983ab125f4
JWT_REFRESH_EXPIRES_IN=30d

# Security
BCRYPT_SALT_ROUNDS=12
SESSION_SECRET=production-session-secret-key-change-this

# CORS Configuration
CORS_ORIGIN=https://www.sonalibd.org
FRONTEND_URL=https://www.sonalibd.org
API_URL=https://www.sonalibd.org/api

# Domain Configuration
DOMAIN=www.sonalibd.org

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/var/www/sonali-app/uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_MAX=5

# Logging
LOG_LEVEL=info
LOG_FILE=/var/www/sonali-app/logs/app.log
