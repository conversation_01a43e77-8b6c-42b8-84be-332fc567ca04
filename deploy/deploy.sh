#!/bin/bash

# Sonali App Native Deployment Script for VPS (No Docker)
# Usage: ./deploy.sh [environment]
# Environment: development, staging, production (default: production)

set -e

# Configuration
VPS_HOST="**************"
VPS_USER="root"
APP_NAME="sonali-app"
DEPLOY_PATH="/var/www/$APP_NAME"
ENVIRONMENT="${1:-production}"

echo "🚀 Starting deployment to $VPS_HOST for $ENVIRONMENT environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment file exists
if [ ! -f "deploy/.env.$ENVIRONMENT" ]; then
    print_error "Environment file deploy/.env.$ENVIRONMENT not found!"
    exit 1
fi

# Create deployment package
print_status "Creating deployment package..."
tar -czf sonali-app-deploy.tar.gz \
    --exclude='node_modules' \
    --exclude='.git' \
    --exclude='dist' \
    --exclude='*.log' \
    --exclude='.env' \
    .

# Upload to VPS
print_status "Uploading to VPS..."
scp sonali-app-deploy.tar.gz $VPS_USER@$VPS_HOST:/tmp/

# Deploy on VPS
print_status "Deploying on VPS..."
ssh $VPS_USER@$VPS_HOST << EOF
    set -e
    
    # Create deployment directory
    mkdir -p $DEPLOY_PATH
    cd $DEPLOY_PATH
    
    # Backup current deployment if exists
    if [ -d "current" ]; then
        echo "Backing up current deployment..."
        mv current backup-\$(date +%Y%m%d-%H%M%S) || true
    fi
    
    # Extract new deployment
    echo "Extracting new deployment..."
    mkdir -p current
    cd current
    tar -xzf /tmp/sonali-app-deploy.tar.gz
    
    # Copy environment file
    cp deploy/.env.$ENVIRONMENT backend/.env

    # Install Node.js if not installed
    if ! command -v node &> /dev/null; then
        echo "Installing Node.js 20 LTS..."
        curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
        apt-get install -y nodejs
    fi

    # Install PM2 globally if not installed
    if ! command -v pm2 &> /dev/null; then
        echo "Installing PM2..."
        npm install -g pm2
    fi

    # Install dependencies
    echo "Installing dependencies..."
    npm run install:all

    # Build the application
    echo "Building application..."
    npm run build

    # Stop existing PM2 processes
    echo "Stopping existing PM2 processes..."
    pm2 delete all || true

    # Start application with PM2
    echo "Starting application with PM2..."
    pm2 start ecosystem.config.js --env $ENVIRONMENT
    
    # Wait for services to be healthy
    echo "Waiting for services to be healthy..."
    sleep 15

    # Save PM2 configuration
    pm2 save
    pm2 startup

    # Check if services are running
    if pm2 list | grep -q "online"; then
        echo "✅ Deployment successful!"
        echo "🌐 Application should be available at: https://www.sonalibd.org"
        echo "📊 API health check: https://www.sonalibd.org/health"
    else
        echo "❌ Deployment failed! Check PM2 logs:"
        pm2 logs --lines 50
        exit 1
    fi
    
    # Cleanup
    rm -f /tmp/sonali-app-deploy.tar.gz
    
    # Remove old backups (keep last 5)
    cd $DEPLOY_PATH
    ls -t backup-* 2>/dev/null | tail -n +6 | xargs rm -rf || true
EOF

# Cleanup local files
rm -f sonali-app-deploy.tar.gz

print_status "Deployment completed successfully!"
print_status "Application URL: https://www.sonalibd.org"
print_status "API Health Check: https://www.sonalibd.org/health"

# Optional: Run health check
print_status "Running health check..."
sleep 10
if curl -f "https://www.sonalibd.org/health" > /dev/null 2>&1; then
    print_status "✅ Health check passed!"
else
    print_warning "⚠️  Health check failed. Please check the application manually."
fi

echo ""
echo "🎉 Deployment complete!"
echo "📝 Next steps:"
echo "   1. Domain is already configured: www.sonalibd.org"
echo "   2. SSL certificates are already set up"
echo "   3. Nginx is configured and running"
echo "   4. PM2 is managing the application processes"
echo "   5. Set up monitoring and backups as needed"
