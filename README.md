# Sonali App

A modern full-stack web application built with React 19, TypeScript, Node.js, Express, and MySQL.

## 🚀 Tech Stack

### Frontend
- **React 19** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **React Router** for navigation
- **TanStack Query** for data fetching
- **Zustand** for state management
- **React Hook Form** with Zod validation

### Backend
- **Node.js 20 LTS** with TypeScript
- **Express.js** web framework
- **MySQL 8.x** database
- **Prisma ORM** with TypeScript
- **JWT** authentication
- **bcryptjs** for password hashing
- **Express Rate Limit** for API protection

### DevOps
- **PM2** for process management
- **Nginx** for production web server
- **Native VPS deployment** (no Docker dependencies)
- **Automated setup scripts** included

## 📁 Project Structure

```
sonali-app/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API service functions
│   │   ├── types/          # TypeScript type definitions
│   │   ├── utils/          # Utility functions
│   │   └── styles/         # CSS and styling files
│   ├── public/             # Static assets
│   └── package.json
├── backend/                 # Node.js backend API
│   ├── src/
│   │   ├── controllers/    # Request handlers
│   │   ├── models/         # Database models
│   │   ├── routes/         # API route definitions
│   │   ├── middleware/     # Express middleware
│   │   ├── services/       # Business logic
│   │   ├── utils/          # Utility functions
│   │   └── config/         # Configuration files
│   └── package.json
├── shared/                  # Shared TypeScript types
│   └── types/
├── deploy/                  # Deployment configurations
├── scripts/                 # Setup and utility scripts
└── ecosystem.config.js      # PM2 process configuration
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 20 LTS
- MySQL 8.x
- PM2 (for production)

### Quick Start (Native Setup)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sonali-app
   ```

2. **Run development setup**
   ```bash
   chmod +x scripts/setup-dev.sh
   ./scripts/setup-dev.sh
   ```

3. **Start development servers**
   ```bash
   npm run dev
   ```

4. **Access the application**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3001
   - API Health: http://localhost:3001/health

### Manual Setup

1. **Install dependencies**
   ```bash
   npm run install:all
   ```

2. **Database Setup**
   ```bash
   # Copy environment file
   cp deploy/.env.development backend/.env
   # Edit backend/.env with your database credentials

   # Run database migrations
   cd backend && npx prisma migrate dev

   # Seed the database
   npx prisma db seed
   ```

3. **Build the application**
   ```bash
   npm run build
   ```

4. **Start development servers**
   ```bash
   npm run dev
   ```

## 🚀 Deployment

### VPS Deployment (Native - No Docker)

The project includes automated deployment scripts for native VPS deployment.

#### First-time VPS Setup

1. **Run VPS setup script** (on your VPS)
   ```bash
   wget https://raw.githubusercontent.com/your-repo/sonali-app/main/scripts/setup-vps.sh
   chmod +x setup-vps.sh
   ./setup-vps.sh
   ```

#### Application Deployment

1. **Clone and setup**
   ```bash
   cd /var/www/sonali-app
   git clone <your-repo-url> .
   npm run setup
   ```

2. **Configure environment**
   ```bash
   cp deploy/.env.production backend/.env
   # Edit backend/.env with your production values
   ```

3. **Deploy**
   ```bash
   npm run deploy:production
   ```

#### Available Deployment Commands

```bash
npm run deploy              # Quick deploy (build + restart PM2)
npm run deploy:production   # Production deploy
npm run pm2:start          # Start PM2 processes
npm run pm2:stop           # Stop PM2 processes
npm run pm2:restart        # Restart PM2 processes
npm run pm2:logs           # View PM2 logs
```

### Environment Variables

#### Backend (.env)
```env
# Application
NODE_ENV=production
PORT=3001
APP_NAME=sonali-app

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sonali_app_production
DB_USER=sonali_user
DB_PASSWORD=your-secure-password
DATABASE_URL=mysql://sonali_user:your-secure-password@localhost:3306/sonali_app_production

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=30d

# Security
BCRYPT_SALT_ROUNDS=12
CORS_ORIGIN=https://www.sonalibd.org
```

#### Frontend (.env)
```env
# API
VITE_API_URL=https://www.sonalibd.org/api

# App
VITE_APP_NAME=Sonali App
VITE_APP_VERSION=1.0.0
```

## 📚 API Documentation

### Complete API Overview (57 Endpoints)

#### 🔐 Authentication APIs (8 endpoints)
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login (Member ID or email)
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout current session
- `POST /api/auth/logout-all` - Logout from all devices
- `POST /api/auth/forgot-password` - Initiate password reset
- `POST /api/auth/reset-password` - Reset password with token
- `GET /api/auth/profile` - Get user profile

#### 👥 User Management APIs (9 endpoints - Admin Only)
- `GET /api/users` - Get all users with pagination and filtering
- `POST /api/users` - Create new user
- `GET /api/users/:id` - Get user by ID
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user
- `POST /api/users/bulk-import` - Bulk import users
- `GET /api/users/export` - Export users data
- `PUT /api/users/:id/reset-password` - Reset user password
- `PUT /api/users/:id/toggle-status` - Activate/deactivate user

#### 🏢 Branch Management APIs (8 endpoints)
- `GET /api/branches` - Get branches (role-based access)
- `POST /api/branches` - Create new branch (admin only)
- `GET /api/branches/:id` - Get branch details
- `PUT /api/branches/:id` - Update branch (admin only)
- `DELETE /api/branches/:id` - Delete branch (admin only)
- `GET /api/branches/:id/users` - Get branch users
- `GET /api/branches/:id/members` - Get branch members
- `GET /api/branches/:id/performance` - Get branch performance metrics

#### 👤 Member Management APIs (8 endpoints)
- `GET /api/members` - Get members (with role-based filtering)
- `POST /api/members` - Create new member
- `GET /api/members/:id` - Get member details
- `PUT /api/members/:id` - Update member
- `DELETE /api/members/:id` - Delete member
- `GET /api/members/search` - Search members with autocomplete
- `POST /api/members/:id/upload-photo` - Upload member photo
- `GET /api/members/export` - Export members data (admin only)

#### 💰 Loan Management APIs (8 endpoints)
- `GET /api/loan-applications` - Get loan applications (role-based filtering)
- `POST /api/loan-applications` - Create new loan application
- `GET /api/loan-applications/:id` - Get loan application details
- `PUT /api/loan-applications/:id` - Update loan application
- `PUT /api/loan-applications/:id/approve` - Approve loan application (manager only)
- `PUT /api/loan-applications/:id/reject` - Reject loan application (manager only)
- `GET /api/loans` - Get active loans
- `POST /api/loans/calculator` - Loan calculation endpoint

#### 💳 Installment Collection APIs (8 endpoints)
- `GET /api/installments` - Get installments (role-based filtering)
- `GET /api/installments/pending` - Get pending installments
- `GET /api/installments/overdue` - Get overdue installments
- `POST /api/installments/:id/collect` - Collect installment payment
- `GET /api/installments/:id/history` - Get installment payment history
- `GET /api/installments/member/:memberId` - Get member installments
- `PUT /api/installments/:id/status` - Update installment status
- `POST /api/installments/update-overdue` - Update overdue installments

#### 📊 Financial Management APIs (8 endpoints)
- `GET /api/transactions` - Get branch transactions (role-based)
- `POST /api/transactions` - Create new transaction (manager only)
- `GET /api/transactions/:id` - Get transaction details
- `PUT /api/transactions/:id` - Update transaction
- `DELETE /api/transactions/:id` - Delete transaction
- `GET /api/transactions/summary` - Get financial summary
- `GET /api/analytics/branch/:branchId` - Get branch analytics (admin/manager)
- `GET /api/analytics/system` - Get system-wide analytics (admin only)

#### 📋 Reports & Analytics
- `POST /api/reports/generate` - Generate custom reports
- Monthly/yearly financial summaries
- Branch performance reports
- Loan portfolio analysis
- Collection reports

### Health Check
- `GET /health` - Server health check

For detailed API documentation with request/response examples, see [API_DOCUMENTATION.md](./API_DOCUMENTATION.md)

## 🧪 Testing

### Security Testing
```bash
# Run comprehensive security tests
cd backend/scripts
./test-security.sh        # Linux/macOS
test-security.bat         # Windows
```

### Unit Testing
```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd frontend
npm test
```

### Manual Testing
- Test Member ID and email login
- Verify rate limiting functionality
- Test password reset flow
- Check session management
- Validate security headers

## 🔧 Available Scripts

### Root Level
- `npm run dev` - Start both frontend and backend in development
- `npm run build` - Build both frontend and backend
- `npm run start` - Start both in production mode
- `npm run setup` - Install dependencies and build
- `npm run deploy` - Deploy to production (build + PM2 restart)
- `npm run pm2:start` - Start PM2 processes
- `npm run pm2:stop` - Stop PM2 processes
- `npm run pm2:restart` - Restart PM2 processes
- `npm run pm2:logs` - View PM2 logs

### Backend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run tests

### Frontend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm test` - Run tests

## 🛡️ Security Features

### Authentication & Authorization
- **JWT-based authentication** with refresh token rotation
- **Member ID based login** (email or Member ID)
- **Password hashing** with bcryptjs (configurable salt rounds)
- **Role-based access control** middleware
- **Remember me functionality** with extended sessions
- **Session management** with concurrent session limits
- **Token blacklisting** and version control

### Security Protection
- **Rate limiting** for login attempts with progressive lockout
- **Password reset** functionality with secure tokens
- **CORS configuration** for www.sonalibd.org domain
- **Input validation** and sanitization (DOMPurify)
- **SQL injection prevention** with pattern detection
- **XSS protection** with comprehensive filtering
- **Security headers** (Helmet.js, CSP, HSTS)
- **Request size validation** and monitoring

### Frontend Security
- **Code obfuscation** with Webpack/Vite configuration
- **Console log removal** in production builds
- **Source map protection** (disabled in production)
- **Asset name randomization** for security
- **Bundle optimization** and tree shaking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please open an issue in the repository.

---

Built with ❤️ by the Sonali App Team
