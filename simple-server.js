const http = require('http');
const url = require('url');

const PORT = process.env.PORT || 3001;

const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;

  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Health check endpoint
  if (path === '/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      status: 'OK',
      message: '<PERSON>ali App Backend is running',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    }));
    return;
  }

  // API status endpoint
  if (path === '/api/status') {
    res.writeHead(200);
    res.end(JSON.stringify({
      message: 'Sonali App API is working',
      version: '1.0.0'
    }));
    return;
  }

  // Test credentials endpoint
  if (path === '/api/auth/test-credentials') {
    res.writeHead(200);
    res.end(JSON.stringify({
      message: 'Default login credentials for testing',
      credentials: [
        { role: 'admin', email: '<EMAIL>', password: 'admin123' },
        { role: 'manager', email: '<EMAIL>', password: 'manager123' },
        { role: 'field_officer', email: '<EMAIL>', password: 'officer123' }
      ]
    }));
    return;
  }

  // Default response for other API routes
  if (path.startsWith('/api/')) {
    res.writeHead(404);
    res.end(JSON.stringify({
      error: 'API endpoint not found',
      message: 'This is a simplified server for testing. Full API implementation is in development.'
    }));
    return;
  }

  // Default 404
  res.writeHead(404);
  res.end(JSON.stringify({ error: 'Not Found' }));
});

server.listen(PORT, () => {
  console.log(`🚀 Sonali App Backend running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API status: http://localhost:${PORT}/api/status`);
  console.log(`🔑 Test credentials: http://localhost:${PORT}/api/auth/test-credentials`);
});

module.exports = server;
