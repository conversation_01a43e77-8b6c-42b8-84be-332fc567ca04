#!/bin/bash

# Sonali App Local Development Setup Script (No Docker)
# This script sets up the development environment for Sonali App

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_header "🚀 Setting up Sonali App Development Environment"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 20 LTS first."
    print_status "Visit: https://nodejs.org/en/download/"
    exit 1
fi

# Check Node.js version
node_version=$(node --version)
print_status "Node.js version: $node_version"

# Check if npm is available
if ! command -v npm &> /dev/null; then
    print_error "NPM is not installed. Please install NPM first."
    exit 1
fi

npm_version=$(npm --version)
print_status "NPM version: $npm_version"

# Install dependencies
print_status "Installing root dependencies..."
npm install

print_status "Installing frontend dependencies..."
cd frontend && npm install && cd ..

print_status "Installing backend dependencies..."
cd backend && npm install && cd ..

# Create environment files if they don't exist
print_status "Setting up environment files..."

if [ ! -f "backend/.env" ]; then
    print_status "Creating backend .env file..."
    cp deploy/.env.development backend/.env
    print_warning "Please update backend/.env with your local database credentials"
fi

if [ ! -f "frontend/.env" ]; then
    print_status "Creating frontend .env file..."
    cat > frontend/.env << EOF
VITE_API_URL=http://localhost:3001/api
VITE_APP_NAME=Sonali App
VITE_APP_VERSION=1.0.0
EOF
fi

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p logs
mkdir -p uploads
mkdir -p backups

# Check if MySQL is running (optional for development)
if command -v mysql &> /dev/null; then
    if systemctl is-active --quiet mysql; then
        print_status "MySQL is running"
    else
        print_warning "MySQL is not running. You may need to start it manually."
        print_status "Ubuntu/Debian: sudo systemctl start mysql"
        print_status "macOS: brew services start mysql"
        print_status "Windows: Start MySQL service from Services panel"
    fi
else
    print_warning "MySQL is not installed. Please install MySQL for database functionality."
    print_status "Ubuntu/Debian: sudo apt install mysql-server"
    print_status "macOS: brew install mysql"
    print_status "Windows: Download from https://dev.mysql.com/downloads/mysql/"
fi

# Build the application
print_status "Building the application..."
npm run build

print_status "✅ Development environment setup completed!"
print_status ""
print_status "📝 Next steps:"
print_status "1. Set up your MySQL database:"
print_status "   - Create database: sonali_app_development"
print_status "   - Update backend/.env with your database credentials"
print_status "2. Run database migrations: cd backend && npx prisma migrate dev"
print_status "3. Seed the database: cd backend && npx prisma db seed"
print_status "4. Start development servers: npm run dev"
print_status ""
print_status "🔧 Available commands:"
print_status "- npm run dev          # Start both frontend and backend in development mode"
print_status "- npm run dev:frontend # Start only frontend development server"
print_status "- npm run dev:backend  # Start only backend development server"
print_status "- npm run build        # Build both frontend and backend"
print_status "- npm run start        # Start both in production mode"
print_status ""
print_status "🌐 Development URLs:"
print_status "- Frontend: http://localhost:5173"
print_status "- Backend API: http://localhost:3001"
print_status "- API Health: http://localhost:3001/health"
print_status ""
print_status "🎉 Happy coding!"
