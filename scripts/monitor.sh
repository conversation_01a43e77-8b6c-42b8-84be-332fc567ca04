#!/bin/bash

# Sonali App Monitoring Script (Native Deployment)
# This script monitors the health and status of the Sonali App

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/sonali-app"
DOMAIN="www.sonalibd.org"
API_URL="https://$DOMAIN/api"
HEALTH_URL="https://$DOMAIN/health"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[MONITOR]${NC} $1"
}

# Function to check service status
check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        print_success "✅ $service is running"
        return 0
    else
        print_error "❌ $service is not running"
        return 1
    fi
}

# Function to check URL
check_url() {
    local url=$1
    local name=$2
    local response=$(curl -s -o /dev/null -w "%{http_code}" $url 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ]; then
        print_success "✅ $name is accessible (HTTP $response)"
        return 0
    else
        print_error "❌ $name is not accessible (HTTP $response)"
        return 1
    fi
}

print_header "🔍 Sonali App Health Monitor - $(date)"

# Check system resources
print_status "📊 System Resources:"
echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "Memory Usage: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
echo "Disk Usage: $(df -h / | awk 'NR==2{printf "%s", $5}')"
echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"

echo ""

# Check services
print_status "🔧 Service Status:"
check_service nginx
check_service mysql
check_service ufw

echo ""

# Check PM2 processes
print_status "⚙️ PM2 Process Status:"
if command -v pm2 &> /dev/null; then
    pm2_status=$(pm2 jlist 2>/dev/null | jq -r '.[] | "\(.name): \(.pm2_env.status)"' 2>/dev/null || pm2 list --no-color | grep -E "(online|stopped|errored)" || echo "No PM2 processes found")
    echo "$pm2_status"
else
    print_error "PM2 is not installed"
fi

echo ""

# Check application URLs
print_status "🌐 Application Accessibility:"
check_url "https://$DOMAIN" "Frontend"
check_url "$HEALTH_URL" "Health Check"
check_url "$API_URL/auth/test-credentials" "API"

echo ""

# Check SSL certificate
print_status "🔒 SSL Certificate:"
ssl_expiry=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
if [ ! -z "$ssl_expiry" ]; then
    print_success "✅ SSL Certificate expires: $ssl_expiry"
else
    print_error "❌ Could not retrieve SSL certificate information"
fi

echo ""

# Check database connectivity
print_status "🗄️ Database Status:"
if mysql -u sonali_user -pfe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8 -e "SELECT 1" sonali_app_production >/dev/null 2>&1; then
    print_success "✅ Database is accessible"
    
    # Get database stats
    user_count=$(mysql -u sonali_user -pfe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8 sonali_app_production -e "SELECT COUNT(*) FROM users;" 2>/dev/null | tail -1)
    member_count=$(mysql -u sonali_user -pfe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8 sonali_app_production -e "SELECT COUNT(*) FROM members;" 2>/dev/null | tail -1)
    
    echo "  Users: $user_count"
    echo "  Members: $member_count"
else
    print_error "❌ Database is not accessible"
fi

echo ""

# Check log files
print_status "📝 Recent Logs:"
if [ -f "$APP_DIR/logs/backend-error-0.log" ]; then
    error_count=$(tail -100 $APP_DIR/logs/backend-error-0.log 2>/dev/null | wc -l)
    if [ $error_count -gt 0 ]; then
        print_warning "⚠️ $error_count recent error log entries"
        echo "Recent errors:"
        tail -5 $APP_DIR/logs/backend-error-0.log 2>/dev/null | sed 's/^/  /'
    else
        print_success "✅ No recent errors in logs"
    fi
else
    print_warning "⚠️ Error log file not found"
fi

echo ""

# Check disk space
print_status "💾 Disk Space:"
disk_usage=$(df -h $APP_DIR | awk 'NR==2{print $5}' | sed 's/%//')
if [ $disk_usage -gt 80 ]; then
    print_error "❌ Disk usage is high: ${disk_usage}%"
elif [ $disk_usage -gt 70 ]; then
    print_warning "⚠️ Disk usage is moderate: ${disk_usage}%"
else
    print_success "✅ Disk usage is normal: ${disk_usage}%"
fi

echo ""

# Check for updates
print_status "🔄 System Updates:"
update_count=$(apt list --upgradable 2>/dev/null | wc -l)
if [ $update_count -gt 1 ]; then
    print_warning "⚠️ $((update_count-1)) package updates available"
else
    print_success "✅ System is up to date"
fi

echo ""

# Performance metrics
print_status "⚡ Performance Metrics:"
response_time=$(curl -o /dev/null -s -w "%{time_total}" $HEALTH_URL 2>/dev/null || echo "N/A")
echo "API Response Time: ${response_time}s"

# Check if response time is acceptable
if [ "$response_time" != "N/A" ] && [ $(echo "$response_time > 2.0" | bc -l 2>/dev/null || echo 0) -eq 1 ]; then
    print_warning "⚠️ API response time is slow"
elif [ "$response_time" != "N/A" ]; then
    print_success "✅ API response time is good"
fi

echo ""
print_header "🎯 Monitor Summary Complete - $(date)"

# Exit with error code if any critical services are down
if ! systemctl is-active --quiet nginx || ! systemctl is-active --quiet mysql; then
    exit 1
fi
