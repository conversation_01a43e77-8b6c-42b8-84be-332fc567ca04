#!/bin/bash

# Sonali App VPS Setup Script (No Docker)
# This script sets up the complete environment for Sonali App on a fresh Ubuntu VPS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_header "🚀 Starting Sonali App VPS Setup (Native - No Docker)"

# Update system
print_status "Updating system packages..."
apt update && apt upgrade -y

# Install essential packages
print_status "Installing essential packages..."
apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release

# Install Node.js 20 LTS
print_status "Installing Node.js 20 LTS..."
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt-get install -y nodejs

# Verify Node.js installation
node_version=$(node --version)
npm_version=$(npm --version)
print_status "Node.js version: $node_version"
print_status "NPM version: $npm_version"

# Install PM2 globally
print_status "Installing PM2 process manager..."
npm install -g pm2

# Install MySQL 8.0
print_status "Installing MySQL 8.0..."
apt install -y mysql-server mysql-client

# Secure MySQL installation
print_status "Securing MySQL installation..."
mysql_secure_installation

# Install Nginx
print_status "Installing Nginx..."
apt install -y nginx

# Install Certbot for SSL
print_status "Installing Certbot for SSL certificates..."
apt install -y certbot python3-certbot-nginx

# Create application directory
print_status "Creating application directory..."
mkdir -p /var/www/sonali-app
mkdir -p /var/www/sonali-app/logs
mkdir -p /var/www/sonali-app/uploads
mkdir -p /var/www/sonali-app/backups

# Set proper permissions
print_status "Setting proper permissions..."
chown -R www-data:www-data /var/www/sonali-app
chmod -R 755 /var/www/sonali-app

# Configure firewall
print_status "Configuring UFW firewall..."
ufw allow OpenSSH
ufw allow 'Nginx Full'
ufw allow 80
ufw allow 443
ufw --force enable

# Start and enable services
print_status "Starting and enabling services..."
systemctl start nginx
systemctl enable nginx
systemctl start mysql
systemctl enable mysql

# Create MySQL database and user
print_status "Setting up MySQL database..."
mysql -e "CREATE DATABASE IF NOT EXISTS sonali_app_production;"
mysql -e "CREATE DATABASE IF NOT EXISTS sonali_app_development;"
mysql -e "CREATE USER IF NOT EXISTS 'sonali_user'@'localhost' IDENTIFIED BY 'fe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8';"
mysql -e "GRANT ALL PRIVILEGES ON sonali_app_production.* TO 'sonali_user'@'localhost';"
mysql -e "GRANT ALL PRIVILEGES ON sonali_app_development.* TO 'sonali_user'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

print_status "✅ VPS setup completed successfully!"
print_status ""
print_status "📝 Next steps:"
print_status "1. Clone your Sonali App repository to /var/www/sonali-app"
print_status "2. Run 'npm run setup' to install dependencies and build"
print_status "3. Configure your domain DNS to point to this server"
print_status "4. Run 'certbot --nginx -d yourdomain.com' to set up SSL"
print_status "5. Start the application with 'npm run pm2:start'"
print_status ""
print_status "🔧 Installed components:"
print_status "- Node.js $node_version"
print_status "- NPM $npm_version"
print_status "- PM2 (process manager)"
print_status "- MySQL 8.0"
print_status "- Nginx"
print_status "- Certbot (SSL certificates)"
print_status "- UFW Firewall (configured)"
print_status ""
print_status "🎉 Your VPS is ready for Sonali App deployment!"
