#!/bin/bash

# Sonali App Backup Script (Native Deployment)
# This script creates backups of the database, application files, and configurations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/var/www/sonali-app"
BACKUP_DIR="/var/www/sonali-app/backups"
DB_NAME="sonali_app_production"
DB_USER="sonali_user"
DB_PASSWORD="fe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS=30

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[BACKUP]${NC} $1"
}

print_header "🗄️ Starting Sonali App Backup - $TIMESTAMP"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Database backup
print_status "Creating database backup..."
mysqldump -u $DB_USER -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/database_$TIMESTAMP.sql

if [ $? -eq 0 ]; then
    print_status "✅ Database backup completed: database_$TIMESTAMP.sql"
else
    print_error "❌ Database backup failed"
    exit 1
fi

# Application files backup
print_status "Creating application files backup..."
tar -czf $BACKUP_DIR/app_files_$TIMESTAMP.tar.gz \
    --exclude='node_modules' \
    --exclude='dist' \
    --exclude='logs' \
    --exclude='backups' \
    --exclude='.git' \
    -C $APP_DIR .

if [ $? -eq 0 ]; then
    print_status "✅ Application files backup completed: app_files_$TIMESTAMP.tar.gz"
else
    print_error "❌ Application files backup failed"
    exit 1
fi

# Configuration backup
print_status "Creating configuration backup..."
tar -czf $BACKUP_DIR/config_$TIMESTAMP.tar.gz \
    /etc/nginx/sites-available/sonali-app \
    /etc/letsencrypt/live/www.sonalibd.org \
    $APP_DIR/ecosystem.config.js \
    $APP_DIR/backend/.env 2>/dev/null || true

if [ $? -eq 0 ]; then
    print_status "✅ Configuration backup completed: config_$TIMESTAMP.tar.gz"
else
    print_warning "⚠️ Some configuration files may not have been backed up"
fi

# PM2 configuration backup
print_status "Creating PM2 configuration backup..."
pm2 save
cp ~/.pm2/dump.pm2 $BACKUP_DIR/pm2_dump_$TIMESTAMP.pm2 2>/dev/null || true

# Create a complete backup archive
print_status "Creating complete backup archive..."
tar -czf $BACKUP_DIR/complete_backup_$TIMESTAMP.tar.gz \
    $BACKUP_DIR/database_$TIMESTAMP.sql \
    $BACKUP_DIR/app_files_$TIMESTAMP.tar.gz \
    $BACKUP_DIR/config_$TIMESTAMP.tar.gz \
    $BACKUP_DIR/pm2_dump_$TIMESTAMP.pm2 2>/dev/null || true

# Calculate backup sizes
db_size=$(du -h $BACKUP_DIR/database_$TIMESTAMP.sql | cut -f1)
app_size=$(du -h $BACKUP_DIR/app_files_$TIMESTAMP.tar.gz | cut -f1)
complete_size=$(du -h $BACKUP_DIR/complete_backup_$TIMESTAMP.tar.gz | cut -f1)

print_status "📊 Backup sizes:"
print_status "  Database: $db_size"
print_status "  App files: $app_size"
print_status "  Complete backup: $complete_size"

# Clean up old backups
print_status "Cleaning up old backups (older than $RETENTION_DAYS days)..."
find $BACKUP_DIR -name "*.sql" -type f -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.tar.gz" -type f -mtime +$RETENTION_DAYS -delete
find $BACKUP_DIR -name "*.pm2" -type f -mtime +$RETENTION_DAYS -delete

# List current backups
print_status "📋 Current backups:"
ls -lh $BACKUP_DIR/ | grep $TIMESTAMP

print_status "✅ Backup completed successfully!"
print_status "📁 Backup location: $BACKUP_DIR"
print_status "🕒 Timestamp: $TIMESTAMP"

# Optional: Upload to remote storage (uncomment and configure as needed)
# print_status "Uploading to remote storage..."
# rsync -avz $BACKUP_DIR/complete_backup_$TIMESTAMP.tar.gz user@backup-server:/path/to/backups/

print_status "🎉 Backup process completed!"
