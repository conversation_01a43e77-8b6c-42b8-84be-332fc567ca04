module.exports = {
  apps: [
    {
      name: 'sonali-backend',
      script: './production-server.js',
      cwd: '/var/www/sonali-app',
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
        DB_HOST: 'localhost',
        DB_PORT: 3306,
        DB_NAME: 'sonali_app_production',
        DB_USER: 'sonali_user',
        DB_PASSWORD: 'sonali_password',
        DATABASE_URL: 'mysql://sonali_user:sonali_password@localhost:3306/sonali_app_production',
        JWT_SECRET: 'production-super-secret-jwt-key-change-this-in-real-production',
        JWT_EXPIRES_IN: '7d',
        JWT_REFRESH_SECRET: 'production-refresh-secret-key-change-this-too',
        JWT_REFRESH_EXPIRES_IN: '30d',
        BCRYPT_SALT_ROUNDS: 12,
        CORS_ORIGIN: 'https://www.sonalibd.org'
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
