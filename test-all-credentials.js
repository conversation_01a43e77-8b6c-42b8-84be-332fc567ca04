const testAllCredentials = async () => {
    const credentials = [
        { identifier: 'ADMIN001', password: 'admin123', name: '<PERSON><PERSON> (Admin)' },
        { identifier: 'MGR001', password: 'manager123', name: '<PERSON><PERSON> (Manager)' },
        { identifier: 'FO001', password: 'officer123', name: '<PERSON><PERSON> (Field Officer)' },
        { identifier: 'MEM001', password: 'member123', name: '<PERSON><PERSON> (Member)' }
    ];
    
    console.log('🔐 Testing all user credentials...\n');
    
    for (const cred of credentials) {
        try {
            console.log(`Testing ${cred.name}...`);
            
            const response = await fetch('http://localhost:3001/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    identifier: cred.identifier,
                    password: cred.password,
                    rememberMe: false
                })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                const user = data.user || data.data?.user;
                console.log(`✅ ${cred.name}: Login successful`);
                console.log(`   User: ${user?.name}`);
                console.log(`   Role: ${user?.role}`);
                console.log(`   Member ID: ${user?.memberId}`);
                console.log(`   Active: ${user?.isActive}`);
                console.log(`   Token: ${(data.accessToken || data.data?.accessToken)?.substring(0, 20)}...`);
            } else {
                console.log(`❌ ${cred.name}: Login failed`);
                console.log(`   Error: ${data.error || data.message || 'Unknown error'}`);
                console.log(`   Status: ${response.status}`);
            }
            
            console.log(''); // Empty line for readability
            
        } catch (error) {
            console.log(`❌ ${cred.name}: Network error`);
            console.log(`   Error: ${error.message}`);
            console.log('');
        }
    }
    
    console.log('🎯 Testing complete!');
};

// Also test with email addresses
const testEmailCredentials = async () => {
    const emailCredentials = [
        { identifier: '<EMAIL>', password: 'admin123', name: 'Korim (Admin) via Email' },
        { identifier: '<EMAIL>', password: 'manager123', name: 'Rajib (Manager) via Email' },
        { identifier: '<EMAIL>', password: 'officer123', name: 'Hamid (Field Officer) via Email' },
        { identifier: '<EMAIL>', password: 'member123', name: 'Alim (Member) via Email' }
    ];
    
    console.log('\n📧 Testing email-based login...\n');
    
    for (const cred of emailCredentials) {
        try {
            console.log(`Testing ${cred.name}...`);
            
            const response = await fetch('http://localhost:3001/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    identifier: cred.identifier,
                    password: cred.password,
                    rememberMe: false
                })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                const user = data.user || data.data?.user;
                console.log(`✅ ${cred.name}: Login successful`);
                console.log(`   User: ${user?.name}`);
                console.log(`   Role: ${user?.role}`);
            } else {
                console.log(`❌ ${cred.name}: Login failed`);
                console.log(`   Error: ${data.error || data.message || 'Unknown error'}`);
            }
            
            console.log('');
            
        } catch (error) {
            console.log(`❌ ${cred.name}: Network error - ${error.message}`);
            console.log('');
        }
    }
};

const runAllTests = async () => {
    await testAllCredentials();
    await testEmailCredentials();
};

runAllTests().catch(console.error);
