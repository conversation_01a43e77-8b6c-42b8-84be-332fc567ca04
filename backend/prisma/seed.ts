import { PrismaClient, UserRole, SavingType, SavingMethod, LoanApplicationStatus, RepaymentMethod, InstallmentStatus, EntryType } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create main branch
  const mainBranch = await prisma.branch.create({
    data: {
      name: 'Main Branch',
      address: 'Dhaka, Bangladesh',
      isActive: true,
    },
  });

  console.log('✅ Created main branch');

  // Create admin user - Korim
  const hashedPassword = await bcrypt.hash('admin123', 12);
  const adminUser = await prisma.user.create({
    data: {
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      password: hashedPassword,
      role: UserRole.admin,
      memberId: 'ADMIN001',
      isActive: true,
      branchId: mainBranch.id,
    },
  });

  console.log('✅ Created admin user - <PERSON><PERSON>');

  // Create branch manager - <PERSON>ib
  const managerPassword = await bcrypt.hash('manager123', 12);
  const managerUser = await prisma.user.create({
    data: {
      name: 'Rajib',
      email: '<EMAIL>',
      password: managerPassword,
      role: UserRole.manager,
      memberId: 'MGR001',
      isActive: true,
      branchId: mainBranch.id,
    },
  });

  // Update branch with manager
  await prisma.branch.update({
    where: { id: mainBranch.id },
    data: { managerId: managerUser.id },
  });

  console.log('✅ Created branch manager - Rajib');

  // Create field officer - Hamid
  const officerPassword = await bcrypt.hash('officer123', 12);
  const fieldOfficer = await prisma.user.create({
    data: {
      name: 'Hamid',
      email: '<EMAIL>',
      password: officerPassword,
      role: UserRole.field_officer,
      memberId: 'FO001',
      isActive: true,
      branchId: mainBranch.id,
    },
  });

  console.log('✅ Created field officer - Hamid');

  // Create member user - Alim
  const memberPassword = await bcrypt.hash('member123', 12);
  const memberUser = await prisma.user.create({
    data: {
      name: 'Alim',
      email: '<EMAIL>',
      password: memberPassword,
      role: UserRole.member,
      memberId: 'MEM001',
      isActive: true,
      branchId: mainBranch.id,
    },
  });

  console.log('✅ Created member user - Alim');

  // Create sample members with realistic Bangladesh data
  const member1 = await prisma.member.create({
    data: {
      memberId: 'MEM001',
      name: 'Alim',
      fatherOrHusbandName: 'Abdul Karim',
      motherName: 'Rashida Begum',
      presentAddress: 'House 45, Road 12, Dhanmondi, Dhaka-1205',
      permanentAddress: 'Village: Sreepur, Upazila: Savar, District: Dhaka',
      nidNumber: '1987654321098',
      dateOfBirth: new Date('1988-03-20'),
      religion: 'Islam',
      phoneNumber: '+8801712345678',
      bloodGroup: 'B+',
      occupation: 'Small Business Owner',
      branchId: mainBranch.id,
      createdBy: fieldOfficer.id,
      isActive: true,
    },
  });

  const member2 = await prisma.member.create({
    data: {
      memberId: 'MEM002',
      name: 'Rashida Khatun',
      fatherOrHusbandName: 'Mohammad Ali',
      motherName: 'Salma Begum',
      presentAddress: 'House 78, Road 15, Uttara Sector-7, Dhaka-1230',
      permanentAddress: 'Village: Shibaloy, Upazila: Manikganj Sadar, District: Manikganj',
      nidNumber: '9876543210987',
      dateOfBirth: new Date('1990-08-20'),
      religion: 'Islam',
      phoneNumber: '+8801987654321',
      bloodGroup: 'A+',
      occupation: 'Tailoring Business',
      branchId: mainBranch.id,
      createdBy: fieldOfficer.id,
      isActive: true,
      referenceId: member1.id,
    },
  });

  const member3 = await prisma.member.create({
    data: {
      memberId: 'MEM003',
      name: 'Nasir Ahmed',
      fatherOrHusbandName: 'Abdul Majid',
      motherName: 'Fatema Khatun',
      presentAddress: 'House 23, Lane 8, Mirpur-10, Dhaka-1216',
      permanentAddress: 'Village: Goalanda, Upazila: Goalanda, District: Rajbari',
      nidNumber: '5432167890123',
      dateOfBirth: new Date('1985-12-10'),
      religion: 'Islam',
      phoneNumber: '+8801534567890',
      bloodGroup: 'O+',
      occupation: 'Rickshaw Puller',
      branchId: mainBranch.id,
      createdBy: fieldOfficer.id,
      isActive: true,
      referenceId: member1.id,
    },
  });

  const member4 = await prisma.member.create({
    data: {
      memberId: 'MEM004',
      name: 'Salma Begum',
      fatherOrHusbandName: 'Abdur Rahman',
      motherName: 'Jahanara Begum',
      presentAddress: 'House 67, Road 3, Mohammadpur, Dhaka-1207',
      permanentAddress: 'Village: Patgram, Upazila: Patgram, District: Lalmonirhat',
      nidNumber: '7890123456789',
      dateOfBirth: new Date('1992-07-25'),
      religion: 'Islam',
      phoneNumber: '+8801823456789',
      bloodGroup: 'AB+',
      occupation: 'Vegetable Vendor',
      branchId: mainBranch.id,
      createdBy: fieldOfficer.id,
      isActive: true,
      referenceId: member2.id,
    },
  });

  console.log('✅ Created sample members with Bangladesh data');

  // Create sample loan applications
  const loanApplication1 = await prisma.loanApplication.create({
    data: {
      memberId: member1.id,
      appliedAmount: 50000,
      reason: 'Small grocery shop expansion - purchasing rice, lentils, oil and other daily necessities for resale',
      loanCycleNumber: 1,
      recommender: 'Hamid (Field Officer)',
      advancePayment: 5000,
      status: LoanApplicationStatus.approved,
      reviewedBy: managerUser.id,
      reviewedAt: new Date(),
      appliedAt: new Date(),
    },
  });

  const loanApplication2 = await prisma.loanApplication.create({
    data: {
      memberId: member2.id,
      appliedAmount: 30000,
      reason: 'Tailoring business expansion - buying new sewing machine and fabric materials',
      loanCycleNumber: 1,
      recommender: 'Hamid (Field Officer)',
      advancePayment: 3000,
      status: LoanApplicationStatus.approved,
      reviewedBy: managerUser.id,
      reviewedAt: new Date(),
      appliedAt: new Date(),
    },
  });

  const loanApplication3 = await prisma.loanApplication.create({
    data: {
      memberId: member3.id,
      appliedAmount: 25000,
      reason: 'Rickshaw purchase for income generation',
      loanCycleNumber: 1,
      recommender: 'Hamid (Field Officer)',
      status: LoanApplicationStatus.pending,
      appliedAt: new Date(),
    },
  });

  // Create approved loans
  const loan1 = await prisma.loan.create({
    data: {
      loanApplicationId: loanApplication1.id,
      loanDate: new Date(),
      loanAmount: 50000,
      totalRepaymentAmount: 55000,
      repaymentDuration: 12,
      repaymentMethod: RepaymentMethod.monthly,
      installmentCount: 12,
      installmentAmount: 4583.33,
      advancePayment: 5000,
      firstInstallmentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      lastInstallmentDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    },
  });

  const loan2 = await prisma.loan.create({
    data: {
      loanApplicationId: loanApplication2.id,
      loanDate: new Date(),
      loanAmount: 30000,
      totalRepaymentAmount: 33000,
      repaymentDuration: 10,
      repaymentMethod: RepaymentMethod.monthly,
      installmentCount: 10,
      installmentAmount: 3300,
      advancePayment: 3000,
      firstInstallmentDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      lastInstallmentDate: new Date(Date.now() + 300 * 24 * 60 * 60 * 1000), // 10 months from now
    },
  });

  // Create installments for loan 1 (Alim's grocery shop loan)
  for (let i = 1; i <= 12; i++) {
    const installmentDate = new Date(Date.now() + (i * 30 * 24 * 60 * 60 * 1000)); // Monthly installments
    await prisma.installment.create({
      data: {
        loanId: loan1.id,
        installmentNo: i,
        installmentDate,
        installmentAmount: 4583.33,
        status: i <= 2 ? InstallmentStatus.paid : InstallmentStatus.pending,
        collectedBy: i <= 2 ? fieldOfficer.id : null,
        collectionDate: i <= 2 ? new Date(Date.now() - ((3-i) * 30 * 24 * 60 * 60 * 1000)) : null,
      },
    });
  }

  // Create installments for loan 2 (Rashida's tailoring loan)
  for (let i = 1; i <= 10; i++) {
    const installmentDate = new Date(Date.now() + (i * 30 * 24 * 60 * 60 * 1000)); // Monthly installments
    await prisma.installment.create({
      data: {
        loanId: loan2.id,
        installmentNo: i,
        installmentDate,
        installmentAmount: 3300,
        status: i === 1 ? InstallmentStatus.paid : InstallmentStatus.pending,
        collectedBy: i === 1 ? fieldOfficer.id : null,
        collectionDate: i === 1 ? new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)) : null,
      },
    });
  }

  console.log('✅ Created sample loans and installments');

  // Create saving accounts
  await prisma.savingAccount.create({
    data: {
      memberId: member1.id,
      savingType: SavingType.general,
      nomineeName: 'Abdul Karim',
      nomineeRelation: 'Father',
      savingMethod: SavingMethod.monthly,
      monthlyAmount: 1000,
      startDate: new Date(),
      createdBy: fieldOfficer.id,
      isActive: true,
    },
  });

  await prisma.savingAccount.create({
    data: {
      memberId: member2.id,
      savingType: SavingType.dps,
      nomineeName: 'Mohammad Ali',
      nomineeRelation: 'Father',
      savingMethod: SavingMethod.monthly,
      monthlyAmount: 500,
      startDate: new Date(),
      createdBy: fieldOfficer.id,
      isActive: true,
    },
  });

  await prisma.savingAccount.create({
    data: {
      memberId: member3.id,
      savingType: SavingType.general,
      nomineeName: 'Abdul Majid',
      nomineeRelation: 'Father',
      savingMethod: SavingMethod.monthly,
      monthlyAmount: 300,
      startDate: new Date(),
      createdBy: fieldOfficer.id,
      isActive: true,
    },
  });

  await prisma.savingAccount.create({
    data: {
      memberId: member4.id,
      savingType: SavingType.dps,
      nomineeName: 'Abdur Rahman',
      nomineeRelation: 'Father',
      savingMethod: SavingMethod.monthly,
      monthlyAmount: 200,
      startDate: new Date(),
      createdBy: fieldOfficer.id,
      isActive: true,
    },
  });

  console.log('✅ Created saving accounts');

  // Create sample branch transactions
  await prisma.branchTransaction.create({
    data: {
      branchId: mainBranch.id,
      transactionType: 'Loan Disbursement',
      createdBy: managerUser.id,
      entryType: EntryType.expense,
      serialNo: 'TXN001',
      date: new Date(),
      description: 'Loan disbursement to Alim for grocery shop expansion',
      accountNo: 'ACC001',
      category: 'Loan',
      voucherNo: 'VOU001',
      amount: 45000, // 50000 - 5000 advance
      enteredBy: fieldOfficer.id,
    },
  });

  await prisma.branchTransaction.create({
    data: {
      branchId: mainBranch.id,
      transactionType: 'Loan Disbursement',
      createdBy: managerUser.id,
      entryType: EntryType.expense,
      serialNo: 'TXN002',
      date: new Date(),
      description: 'Loan disbursement to Rashida Khatun for tailoring business',
      accountNo: 'ACC002',
      category: 'Loan',
      voucherNo: 'VOU002',
      amount: 27000, // 30000 - 3000 advance
      enteredBy: fieldOfficer.id,
    },
  });

  await prisma.branchTransaction.create({
    data: {
      branchId: mainBranch.id,
      transactionType: 'Installment Collection',
      createdBy: fieldOfficer.id,
      entryType: EntryType.income,
      serialNo: 'TXN003',
      date: new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)),
      description: 'First installment collection from Alim',
      accountNo: 'ACC001',
      category: 'Loan Repayment',
      voucherNo: 'VOU003',
      amount: 4583.33,
      enteredBy: fieldOfficer.id,
    },
  });

  await prisma.branchTransaction.create({
    data: {
      branchId: mainBranch.id,
      transactionType: 'Installment Collection',
      createdBy: fieldOfficer.id,
      entryType: EntryType.income,
      serialNo: 'TXN004',
      date: new Date(),
      description: 'Second installment collection from Alim',
      accountNo: 'ACC001',
      category: 'Loan Repayment',
      voucherNo: 'VOU004',
      amount: 4583.33,
      enteredBy: fieldOfficer.id,
    },
  });

  await prisma.branchTransaction.create({
    data: {
      branchId: mainBranch.id,
      transactionType: 'Installment Collection',
      createdBy: fieldOfficer.id,
      entryType: EntryType.income,
      serialNo: 'TXN005',
      date: new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)),
      description: 'First installment collection from Rashida Khatun',
      accountNo: 'ACC002',
      category: 'Loan Repayment',
      voucherNo: 'VOU005',
      amount: 3300,
      enteredBy: fieldOfficer.id,
    },
  });

  console.log('✅ Created sample transactions');

  // Create sample advertisements with Bangladesh context
  await prisma.advertisement.create({
    data: {
      title: 'সোনালী মাইক্রোফাইন্যান্সে স্বাগতম - আপনার স্বপ্নের সাথী',
      image: 'https://via.placeholder.com/800x400/4F46E5/FFFFFF?text=Sonali+Microfinance',
      linkUrl: '/about',
      isActive: true,
      displayOrder: 1,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    },
  });

  await prisma.advertisement.create({
    data: {
      title: 'ক্ষুদ্র ঋণের জন্য আজই আবেদন করুন - সহজ শর্তে',
      image: 'https://via.placeholder.com/800x400/059669/FFFFFF?text=Micro+Loan+Apply',
      linkUrl: '/loans/apply',
      isActive: true,
      displayOrder: 2,
      startDate: new Date(),
      endDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 6 months from now
    },
  });

  await prisma.advertisement.create({
    data: {
      title: 'মহিলা উদ্যোক্তাদের জন্য বিশেষ ঋণ সুবিধা',
      image: 'https://via.placeholder.com/800x400/DC2626/FFFFFF?text=Women+Entrepreneur',
      linkUrl: '/loans/women-entrepreneur',
      isActive: true,
      displayOrder: 3,
      startDate: new Date(),
      endDate: new Date(Date.now() + 270 * 24 * 60 * 60 * 1000), // 9 months from now
    },
  });

  await prisma.advertisement.create({
    data: {
      title: 'সঞ্চয় করুন, ভবিষ্যৎ গড়ুন - DPS ও সাধারণ সঞ্চয়',
      image: 'https://via.placeholder.com/800x400/F59E0B/FFFFFF?text=Savings+Account',
      linkUrl: '/savings',
      isActive: true,
      displayOrder: 4,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    },
  });

  await prisma.advertisement.create({
    data: {
      title: 'কৃষি ঋণ - ফসল উৎপাদনে আমরা আপনার পাশে',
      image: 'https://via.placeholder.com/800x400/16A34A/FFFFFF?text=Agriculture+Loan',
      linkUrl: '/loans/agriculture',
      isActive: true,
      displayOrder: 5,
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
    },
  });

  await prisma.advertisement.create({
    data: {
      title: 'গ্রুপ ঋণ - একসাথে এগিয়ে চলুন',
      image: 'https://via.placeholder.com/800x400/7C3AED/FFFFFF?text=Group+Loan',
      linkUrl: '/loans/group',
      isActive: true,
      displayOrder: 6,
      startDate: new Date(),
      endDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 6 months from now
    },
  });

  console.log('✅ Created sample advertisements with Bangladesh context');

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Default Login Credentials:');
  console.log('Admin (Korim): <EMAIL> / admin123 (Member ID: ADMIN001)');
  console.log('Manager (Rajib): <EMAIL> / manager123 (Member ID: MGR001)');
  console.log('Field Officer (Hamid): <EMAIL> / officer123 (Member ID: FO001)');
  console.log('Member (Alim): <EMAIL> / member123 (Member ID: MEM001)');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
