const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkUsers() {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        memberId: true,
        role: true,
        isActive: true,
      }
    });
    
    console.log('Current users in database:');
    console.table(users);
    
    // Check if our expected users exist
    const expectedUsers = ['ADMIN001', 'MGR001', 'FO001', 'MEM001'];
    
    for (const memberId of expectedUsers) {
      const user = users.find(u => u.memberId === memberId);
      if (user) {
        console.log(`✅ Found user with member ID: ${memberId} (${user.name})`);
      } else {
        console.log(`❌ Missing user with member ID: ${memberId}`);
      }
    }
    
  } catch (error) {
    console.error('Error checking users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUsers();
