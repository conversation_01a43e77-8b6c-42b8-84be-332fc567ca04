// Token Blacklist Model for JWT invalidation
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';

export interface BlacklistedToken {
  id: string;
  token: string;
  tokenType: 'access' | 'refresh';
  userId: string;
  expiresAt: Date;
  createdAt: Date;
}

export class TokenBlacklistService {
  // Add token to blacklist
  async blacklistToken(
    token: string, 
    tokenType: 'access' | 'refresh', 
    userId: string, 
    expiresAt: Date
  ): Promise<void> {
    try {
      await prisma.$executeRaw`
        INSERT INTO token_blacklist (token, token_type, user_id, expires_at, created_at)
        VALUES (${token}, ${tokenType}, ${userId}, ${expiresAt}, NOW())
        ON DUPLICATE KEY UPDATE created_at = NOW()
      `;
      
      logger.info('Token blacklisted', { tokenType, userId });
    } catch (error) {
      logger.error('Error blacklisting token:', error);
      throw error;
    }
  }

  // Check if token is blacklisted
  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const result = await prisma.$queryRaw<Array<{ count: number }>>`
        SELECT COUNT(*) as count FROM token_blacklist 
        WHERE token = ${token} AND expires_at > NOW()
      `;
      
      return result[0].count > 0;
    } catch (error) {
      logger.error('Error checking token blacklist:', error);
      return false; // Fail open for availability
    }
  }

  // Blacklist all tokens for a user (logout from all devices)
  async blacklistAllUserTokens(userId: string): Promise<void> {
    try {
      // This would require storing active tokens, for now we'll use a different approach
      // We'll add a user token version that gets incremented
      await prisma.user.update({
        where: { id: userId },
        data: { 
          tokenVersion: {
            increment: 1
          }
        }
      });
      
      logger.info('All user tokens invalidated', { userId });
    } catch (error) {
      logger.error('Error invalidating user tokens:', error);
      throw error;
    }
  }

  // Clean up expired tokens (should be run periodically)
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const result = await prisma.$executeRaw`
        DELETE FROM token_blacklist WHERE expires_at <= NOW()
      `;
      
      logger.info('Cleaned up expired blacklisted tokens', { deletedCount: result });
    } catch (error) {
      logger.error('Error cleaning up expired tokens:', error);
    }
  }

  // Get user's active sessions count
  async getActiveSessionsCount(userId: string): Promise<number> {
    try {
      const result = await prisma.$queryRaw<Array<{ count: number }>>`
        SELECT COUNT(DISTINCT session_id) as count 
        FROM user_sessions 
        WHERE user_id = ${userId} AND expires_at > NOW()
      `;
      
      return result[0].count;
    } catch (error) {
      logger.error('Error getting active sessions count:', error);
      return 0;
    }
  }
}

export const tokenBlacklistService = new TokenBlacklistService();
