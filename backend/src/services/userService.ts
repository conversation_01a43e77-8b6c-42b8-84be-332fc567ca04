import { User, UserRole } from '@/models/User';
import { Op } from 'sequelize';
import { logger } from '@/utils/logger';

export interface UserListQuery {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface PaginatedUsers {
  users: Omit<User, 'password'>[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export class UserService {
  async getUsers(query: UserListQuery): Promise<PaginatedUsers> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        role,
        isActive,
        sortBy = 'createdAt',
        sortOrder = 'DESC',
      } = query;

      const offset = (page - 1) * limit;
      const where: any = {};

      // Add search filter
      if (search) {
        where[Op.or] = [
          { firstName: { [Op.like]: `%${search}%` } },
          { lastName: { [Op.like]: `%${search}%` } },
          { email: { [Op.like]: `%${search}%` } },
        ];
      }

      // Add role filter
      if (role) {
        where.role = role;
      }

      // Add active status filter
      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      const { count, rows } = await User.findAndCountAll({
        where,
        limit,
        offset,
        order: [[sortBy, sortOrder]],
        attributes: { exclude: ['password'] },
      });

      const totalPages = Math.ceil(count / limit);

      return {
        users: rows.map(user => user.toJSON()),
        pagination: {
          page,
          limit,
          total: count,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get users error:', error);
      throw error;
    }
  }

  async getUserById(id: string): Promise<Omit<User, 'password'>> {
    try {
      const user = await User.findByPk(id, {
        attributes: { exclude: ['password'] },
      });

      if (!user) {
        throw new Error('User not found');
      }

      return user.toJSON();
    } catch (error) {
      logger.error('Get user by ID error:', error);
      throw error;
    }
  }

  async updateUser(
    id: string,
    data: Partial<Pick<User, 'firstName' | 'lastName' | 'email' | 'role' | 'isActive'>>
  ): Promise<Omit<User, 'password'>> {
    try {
      const user = await User.findByPk(id);
      if (!user) {
        throw new Error('User not found');
      }

      // Check if email is being changed and if it's already taken
      if (data.email && data.email !== user.email) {
        const existingUser = await User.findByEmail(data.email);
        if (existingUser) {
          throw new Error('Email is already taken');
        }
      }

      await user.update(data);

      logger.info('User updated successfully', { userId: user.id, updatedBy: 'admin' });

      return user.toJSON();
    } catch (error) {
      logger.error('Update user error:', error);
      throw error;
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      const user = await User.findByPk(id);
      if (!user) {
        throw new Error('User not found');
      }

      await user.destroy();

      logger.info('User deleted successfully', { userId: id, deletedBy: 'admin' });
    } catch (error) {
      logger.error('Delete user error:', error);
      throw error;
    }
  }

  async toggleUserStatus(id: string): Promise<Omit<User, 'password'>> {
    try {
      const user = await User.findByPk(id);
      if (!user) {
        throw new Error('User not found');
      }

      await user.update({ isActive: !user.isActive });

      logger.info('User status toggled', { 
        userId: id, 
        newStatus: user.isActive,
        updatedBy: 'admin' 
      });

      return user.toJSON();
    } catch (error) {
      logger.error('Toggle user status error:', error);
      throw error;
    }
  }

  async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<UserRole, number>;
  }> {
    try {
      const [total, active, inactive, roleStats] = await Promise.all([
        User.count(),
        User.count({ where: { isActive: true } }),
        User.count({ where: { isActive: false } }),
        User.findAll({
          attributes: ['role'],
          group: ['role'],
          raw: true,
        }),
      ]);

      const byRole = Object.values(UserRole).reduce((acc, role) => {
        acc[role] = 0;
        return acc;
      }, {} as Record<UserRole, number>);

      // This would need to be adjusted based on your Sequelize version
      // The roleStats query above might need modification

      return {
        total,
        active,
        inactive,
        byRole,
      };
    } catch (error) {
      logger.error('Get user stats error:', error);
      throw error;
    }
  }
}

export const userService = new UserService();
