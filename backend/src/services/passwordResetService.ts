import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';
import { prismaUserService } from '@/services/prisma/userService';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import { config } from '@/config';

export interface PasswordResetRequest {
  identifier: string; // email or member ID
  ipAddress: string;
  userAgent: string;
}

export interface PasswordResetVerification {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export class PasswordResetService {
  private readonly TOKEN_EXPIRY_HOURS = 1; // 1 hour
  private readonly MAX_RESET_ATTEMPTS_PER_IP = 5; // per hour
  private readonly MAX_RESET_ATTEMPTS_PER_USER = 3; // per day

  async initiatePasswordReset(data: PasswordResetRequest): Promise<{
    success: boolean;
    message: string;
    resetToken?: string; // Only for development/testing
  }> {
    try {
      // Check rate limiting
      const rateLimitCheck = await this.checkResetRateLimit(data.ipAddress, data.identifier);
      if (!rateLimitCheck.allowed) {
        return {
          success: false,
          message: `Too many reset attempts. Please try again in ${rateLimitCheck.waitMinutes} minutes.`,
        };
      }

      // Find user by email or member ID
      const user = await prismaUserService.findUserByEmailOrMemberId(data.identifier);
      if (!user) {
        // Don't reveal if user exists or not for security
        return {
          success: true,
          message: 'If an account with that identifier exists, a password reset link has been sent.',
        };
      }

      if (!user.isActive) {
        return {
          success: false,
          message: 'Account is deactivated. Please contact support.',
        };
      }

      // Generate secure reset token
      const resetToken = this.generateResetToken();
      const hashedToken = await bcrypt.hash(resetToken, 10);
      const expiresAt = new Date(Date.now() + this.TOKEN_EXPIRY_HOURS * 60 * 60 * 1000);

      // Store reset token in database
      await prisma.user.update({
        where: { id: user.id },
        data: {
          passwordResetToken: hashedToken,
          passwordResetExpires: expiresAt,
        },
      });

      // Record the reset attempt
      await this.recordResetAttempt(user.id, data.ipAddress, data.userAgent);

      // In a real application, you would send an email here
      // await this.sendPasswordResetEmail(user.email, resetToken);

      logger.info('Password reset initiated', {
        userId: user.id,
        email: user.email,
        ipAddress: data.ipAddress,
      });

      return {
        success: true,
        message: 'If an account with that identifier exists, a password reset link has been sent.',
        // Only include token in development for testing
        ...(config.NODE_ENV === 'development' && { resetToken }),
      };
    } catch (error) {
      logger.error('Error initiating password reset:', error);
      return {
        success: false,
        message: 'An error occurred while processing your request. Please try again.',
      };
    }
  }

  async verifyResetToken(token: string): Promise<{
    valid: boolean;
    userId?: string;
    message: string;
  }> {
    try {
      const users = await prisma.user.findMany({
        where: {
          passwordResetToken: { not: null },
          passwordResetExpires: { gt: new Date() },
        },
        select: {
          id: true,
          passwordResetToken: true,
          passwordResetExpires: true,
        },
      });

      for (const user of users) {
        if (user.passwordResetToken) {
          const isValidToken = await bcrypt.compare(token, user.passwordResetToken);
          if (isValidToken) {
            return {
              valid: true,
              userId: user.id,
              message: 'Token is valid',
            };
          }
        }
      }

      return {
        valid: false,
        message: 'Invalid or expired reset token',
      };
    } catch (error) {
      logger.error('Error verifying reset token:', error);
      return {
        valid: false,
        message: 'Error verifying token',
      };
    }
  }

  async resetPassword(data: PasswordResetVerification): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      if (data.newPassword !== data.confirmPassword) {
        return {
          success: false,
          message: 'Passwords do not match',
        };
      }

      // Validate password strength
      const passwordValidation = this.validatePasswordStrength(data.newPassword);
      if (!passwordValidation.valid) {
        return {
          success: false,
          message: passwordValidation.message,
        };
      }

      // Verify token
      const tokenVerification = await this.verifyResetToken(data.token);
      if (!tokenVerification.valid || !tokenVerification.userId) {
        return {
          success: false,
          message: tokenVerification.message,
        };
      }

      // Update password
      await prismaUserService.changePassword(tokenVerification.userId, data.newPassword);

      // Clear reset token
      await prisma.user.update({
        where: { id: tokenVerification.userId },
        data: {
          passwordResetToken: null,
          passwordResetExpires: null,
          failedLoginAttempts: 0, // Reset failed attempts
          lockedUntil: null, // Unlock account if locked
        },
      });

      // Invalidate all user sessions for security
      await prisma.userSession.updateMany({
        where: { userId: tokenVerification.userId },
        data: { isActive: false },
      });

      logger.info('Password reset completed', {
        userId: tokenVerification.userId,
      });

      return {
        success: true,
        message: 'Password has been reset successfully. Please log in with your new password.',
      };
    } catch (error) {
      logger.error('Error resetting password:', error);
      return {
        success: false,
        message: 'An error occurred while resetting your password. Please try again.',
      };
    }
  }

  private generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  private validatePasswordStrength(password: string): {
    valid: boolean;
    message: string;
  } {
    if (password.length < 8) {
      return {
        valid: false,
        message: 'Password must be at least 8 characters long',
      };
    }

    if (!/(?=.*[a-z])/.test(password)) {
      return {
        valid: false,
        message: 'Password must contain at least one lowercase letter',
      };
    }

    if (!/(?=.*[A-Z])/.test(password)) {
      return {
        valid: false,
        message: 'Password must contain at least one uppercase letter',
      };
    }

    if (!/(?=.*\d)/.test(password)) {
      return {
        valid: false,
        message: 'Password must contain at least one number',
      };
    }

    if (!/(?=.*[@$!%*?&])/.test(password)) {
      return {
        valid: false,
        message: 'Password must contain at least one special character (@$!%*?&)',
      };
    }

    return {
      valid: true,
      message: 'Password is valid',
    };
  }

  private async checkResetRateLimit(ipAddress: string, identifier: string): Promise<{
    allowed: boolean;
    waitMinutes: number;
  }> {
    try {
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

      // Check IP-based rate limiting
      const ipAttempts = await prisma.$queryRaw<Array<{ count: number }>>`
        SELECT COUNT(*) as count FROM password_reset_attempts 
        WHERE ip_address = ${ipAddress} AND attempted_at >= ${oneHourAgo}
      `;

      if (ipAttempts[0]?.count >= this.MAX_RESET_ATTEMPTS_PER_IP) {
        return { allowed: false, waitMinutes: 60 };
      }

      // Check user-based rate limiting
      const user = await prismaUserService.findUserByEmailOrMemberId(identifier);
      if (user) {
        const userAttempts = await prisma.$queryRaw<Array<{ count: number }>>`
          SELECT COUNT(*) as count FROM password_reset_attempts 
          WHERE user_id = ${user.id} AND attempted_at >= ${oneDayAgo}
        `;

        if (userAttempts[0]?.count >= this.MAX_RESET_ATTEMPTS_PER_USER) {
          return { allowed: false, waitMinutes: 1440 }; // 24 hours
        }
      }

      return { allowed: true, waitMinutes: 0 };
    } catch (error) {
      logger.error('Error checking reset rate limit:', error);
      return { allowed: true, waitMinutes: 0 }; // Fail open
    }
  }

  private async recordResetAttempt(userId: string, ipAddress: string, userAgent: string): Promise<void> {
    try {
      await prisma.$executeRaw`
        INSERT INTO password_reset_attempts (user_id, ip_address, user_agent, attempted_at)
        VALUES (${userId}, ${ipAddress}, ${userAgent}, NOW())
      `;
    } catch (error) {
      logger.error('Error recording reset attempt:', error);
    }
  }

  async cleanupExpiredTokens(): Promise<void> {
    try {
      const result = await prisma.user.updateMany({
        where: {
          passwordResetExpires: { lt: new Date() },
        },
        data: {
          passwordResetToken: null,
          passwordResetExpires: null,
        },
      });

      logger.info('Cleaned up expired password reset tokens', { updatedCount: result.count });
    } catch (error) {
      logger.error('Error cleaning up expired tokens:', error);
    }
  }
}

export const passwordResetService = new PasswordResetService();
