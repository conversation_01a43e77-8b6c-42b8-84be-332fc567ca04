import { prisma } from '@/config/database';
import { Member, UserRole } from '@prisma/client';
import { logger } from '@/utils/logger';
import * as XLSX from 'xlsx';
import path from 'path';
import fs from 'fs';

export interface CreateMemberData {
  memberId: string;
  name: string;
  fatherOrHusbandName: string;
  motherName: string;
  presentAddress: string;
  permanentAddress: string;
  nidNumber: string;
  dateOfBirth: Date;
  religion: string;
  phoneNumber: string;
  bloodGroup?: string;
  photo?: string;
  occupation: string;
  referenceId?: string;
  branchId: string;
  createdBy: string;
}

export interface UpdateMemberData {
  name?: string;
  fatherOrHusbandName?: string;
  motherName?: string;
  presentAddress?: string;
  permanentAddress?: string;
  nidNumber?: string;
  dateOfBirth?: Date;
  religion?: string;
  phoneNumber?: string;
  bloodGroup?: string;
  photo?: string;
  occupation?: string;
  referenceId?: string;
  isActive?: boolean;
}

export interface MemberListQuery {
  page?: number;
  limit?: number;
  search?: string;
  branchId?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface MemberSearchQuery {
  query: string;
  branchId?: string;
  limit?: number;
}

export class MemberService {
  async getMembers(query: MemberListQuery, userRole: UserRole, userBranchId?: string): Promise<{
    members: (Member & { branch?: any; creator?: any; reference?: any })[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        branchId,
        isActive,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;
      const where: any = {};

      // Role-based filtering
      if (userRole !== UserRole.admin) {
        where.branchId = userBranchId;
      } else if (branchId) {
        where.branchId = branchId;
      }

      // Add search filter
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { memberId: { contains: search, mode: 'insensitive' } },
          { phoneNumber: { contains: search, mode: 'insensitive' } },
          { nidNumber: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Add active status filter
      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      const [members, total] = await Promise.all([
        prisma.member.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            branch: {
              select: {
                id: true,
                name: true,
              },
            },
            creator: {
              select: {
                id: true,
                name: true,
              },
            },
            reference: {
              select: {
                id: true,
                name: true,
                memberId: true,
              },
            },
          },
        }),
        prisma.member.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        members,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get members error:', error);
      throw error;
    }
  }

  async createMember(data: CreateMemberData): Promise<Member> {
    try {
      // Check if member ID already exists
      const existingMemberId = await prisma.member.findUnique({
        where: { memberId: data.memberId },
      });

      if (existingMemberId) {
        throw new Error('Member ID already exists');
      }

      // Check if NID number already exists
      const existingNid = await prisma.member.findUnique({
        where: { nidNumber: data.nidNumber },
      });

      if (existingNid) {
        throw new Error('NID number already exists');
      }

      // Validate reference member if provided
      if (data.referenceId) {
        const referenceMember = await prisma.member.findUnique({
          where: { id: data.referenceId },
          select: { id: true, isActive: true },
        });

        if (!referenceMember) {
          throw new Error('Reference member not found');
        }

        if (!referenceMember.isActive) {
          throw new Error('Reference member is not active');
        }
      }

      const member = await prisma.member.create({
        data: {
          ...data,
          isActive: true,
        },
        include: {
          branch: {
            select: {
              id: true,
              name: true,
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
            },
          },
          reference: {
            select: {
              id: true,
              name: true,
              memberId: true,
            },
          },
        },
      });

      logger.info('Member created', {
        memberId: member.id,
        memberIdNumber: member.memberId,
        name: member.name,
        branchId: member.branchId,
        createdBy: member.createdBy,
      });

      return member;
    } catch (error) {
      logger.error('Create member error:', error);
      throw error;
    }
  }

  async getMemberById(id: string, userRole: UserRole, userBranchId?: string): Promise<Member | null> {
    try {
      const member = await prisma.member.findUnique({
        where: { id },
        include: {
          branch: {
            select: {
              id: true,
              name: true,
              address: true,
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
            },
          },
          reference: {
            select: {
              id: true,
              name: true,
              memberId: true,
            },
          },
          references: {
            select: {
              id: true,
              name: true,
              memberId: true,
            },
          },
        },
      });

      // Role-based access control
      if (member && userRole !== UserRole.admin && member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only view members from your own branch');
      }

      return member;
    } catch (error) {
      logger.error('Get member by ID error:', error);
      throw error;
    }
  }

  async updateMember(id: string, data: UpdateMemberData, userRole: UserRole, userBranchId?: string): Promise<Member> {
    try {
      // Check if member exists and user has access
      const existingMember = await prisma.member.findUnique({
        where: { id },
        select: { id: true, branchId: true, nidNumber: true },
      });

      if (!existingMember) {
        throw new Error('Member not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && existingMember.branchId !== userBranchId) {
        throw new Error('Access denied: You can only update members from your own branch');
      }

      // Check if NID number is being changed and if it's already taken
      if (data.nidNumber && data.nidNumber !== existingMember.nidNumber) {
        const existingNid = await prisma.member.findUnique({
          where: { nidNumber: data.nidNumber },
        });

        if (existingNid) {
          throw new Error('NID number already exists');
        }
      }

      // Validate reference member if provided
      if (data.referenceId) {
        const referenceMember = await prisma.member.findUnique({
          where: { id: data.referenceId },
          select: { id: true, isActive: true },
        });

        if (!referenceMember) {
          throw new Error('Reference member not found');
        }

        if (!referenceMember.isActive) {
          throw new Error('Reference member is not active');
        }
      }

      const member = await prisma.member.update({
        where: { id },
        data,
        include: {
          branch: {
            select: {
              id: true,
              name: true,
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
            },
          },
          reference: {
            select: {
              id: true,
              name: true,
              memberId: true,
            },
          },
        },
      });

      logger.info('Member updated', {
        memberId: member.id,
        updatedFields: Object.keys(data),
      });

      return member;
    } catch (error) {
      logger.error('Update member error:', error);
      throw error;
    }
  }

  async deleteMember(id: string, userRole: UserRole, userBranchId?: string): Promise<void> {
    try {
      // Check if member exists and user has access
      const member = await prisma.member.findUnique({
        where: { id },
        select: { 
          id: true, 
          branchId: true, 
          name: true, 
          memberId: true,
          _count: {
            select: {
              loanApplications: true,
              savingAccounts: true,
            },
          },
        },
      });

      if (!member) {
        throw new Error('Member not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only delete members from your own branch');
      }

      // Check if member has active loans or savings
      if (member._count.loanApplications > 0) {
        throw new Error('Cannot delete member with loan applications');
      }

      if (member._count.savingAccounts > 0) {
        throw new Error('Cannot delete member with savings accounts');
      }

      await prisma.member.delete({
        where: { id }
      });

      logger.info('Member deleted', {
        deletedMemberId: id,
        memberName: member.name,
        memberIdNumber: member.memberId,
      });
    } catch (error) {
      logger.error('Delete member error:', error);
      throw error;
    }
  }

  async searchMembers(query: MemberSearchQuery, userRole: UserRole, userBranchId?: string): Promise<Member[]> {
    try {
      const where: any = {
        isActive: true,
        OR: [
          { name: { contains: query.query, mode: 'insensitive' } },
          { memberId: { contains: query.query, mode: 'insensitive' } },
          { phoneNumber: { contains: query.query, mode: 'insensitive' } },
        ],
      };

      // Role-based filtering
      if (userRole !== UserRole.admin) {
        where.branchId = userBranchId;
      } else if (query.branchId) {
        where.branchId = query.branchId;
      }

      const members = await prisma.member.findMany({
        where,
        take: query.limit || 10,
        select: {
          id: true,
          memberId: true,
          name: true,
          phoneNumber: true,
          branch: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { name: 'asc' },
      });

      return members;
    } catch (error) {
      logger.error('Search members error:', error);
      throw error;
    }
  }

  async uploadMemberPhoto(memberId: string, photoPath: string, userRole: UserRole, userBranchId?: string): Promise<Member> {
    try {
      // Check if member exists and user has access
      const member = await prisma.member.findUnique({
        where: { id: memberId },
        select: { id: true, branchId: true, photo: true },
      });

      if (!member) {
        throw new Error('Member not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only update members from your own branch');
      }

      // Delete old photo if exists
      if (member.photo) {
        const oldPhotoPath = path.join(process.cwd(), 'uploads', member.photo);
        if (fs.existsSync(oldPhotoPath)) {
          fs.unlinkSync(oldPhotoPath);
        }
      }

      const updatedMember = await prisma.member.update({
        where: { id: memberId },
        data: { photo: photoPath },
        include: {
          branch: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      logger.info('Member photo uploaded', {
        memberId: updatedMember.id,
        photoPath,
      });

      return updatedMember;
    } catch (error) {
      logger.error('Upload member photo error:', error);
      throw error;
    }
  }

  async exportMembers(query: MemberListQuery, userRole: UserRole, userBranchId?: string): Promise<Buffer> {
    try {
      // Get all members matching the query (without pagination)
      const { members } = await this.getMembers({
        ...query,
        page: 1,
        limit: 10000, // Large limit to get all members
      }, userRole, userBranchId);

      // Prepare data for export
      const exportData = members.map(member => ({
        'Member ID': member.memberId,
        Name: member.name,
        'Father/Husband Name': member.fatherOrHusbandName,
        'Mother Name': member.motherName,
        'Present Address': member.presentAddress,
        'Permanent Address': member.permanentAddress,
        'NID Number': member.nidNumber,
        'Date of Birth': new Date(member.dateOfBirth).toLocaleDateString(),
        Religion: member.religion,
        'Phone Number': member.phoneNumber,
        'Blood Group': member.bloodGroup || '',
        Occupation: member.occupation,
        Branch: member.branch?.name || '',
        Status: member.isActive ? 'Active' : 'Inactive',
        'Created At': new Date(member.createdAt).toLocaleString(),
      }));

      // Create Excel workbook
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Members');

      // Generate buffer
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      logger.info('Members exported', { count: members.length });

      return buffer;
    } catch (error) {
      logger.error('Export members error:', error);
      throw error;
    }
  }
}

export const memberService = new MemberService();
