import { prisma } from '../config/database';
import { UserRole, LoanApplicationStatus, InstallmentStatus } from '@prisma/client';
import { logger } from '../utils/logger';

export interface AdminDashboardData {
  metrics: {
    totalBranches: number;
    totalUsers: number;
    totalFieldOfficers: number;
    totalMembers: number;
    totalActiveLoans: number;
    totalLoanAmount: number;
    totalCollections: number;
    systemUptime: string;
  };
  financialPerformance: Array<{
    period: string;
    collections: number;
    disbursements: number;
    members: number;
  }>;
  branchPerformance: Array<{
    id: string;
    name: string;
    managerId: string;
    managerName: string;
    totalMembers: number;
    activeLoans: number;
    collectionsThisMonth: number;
    performance: number;
  }>;
  recentActivities: Array<any>;
  quickActions: Array<any>;
  alerts: Array<any>;
}

export interface ManagerDashboardData {
  metrics: {
    totalFieldOfficers: number;
    totalActiveLoans: number;
    totalMembers: number;
    monthlyCollectionTarget: number;
    collectedAmount: number;
    pendingInstallments: number;
    overdueInstallments: number;
    branchIncome: number;
    branchExpenses: number;
  };
  installmentStatus: {
    pending: number;
    overdue: number;
    collected: number;
  };
  fieldOfficerPerformance: Array<{
    id: string;
    name: string;
    membersCount: number;
    collectionsThisMonth: number;
    targetAchievement: number;
  }>;
  recentActivities: Array<any>;
  quickActions: Array<any>;
}

export interface FieldOfficerDashboardData {
  metrics: {
    totalMembers: number;
    totalCollections: number;
    monthlyTarget: number;
    achievementPercentage: number;
    pendingInstallments: number;
    overdueInstallments: number;
  };
  recentActivities: Array<any>;
  quickActions: Array<any>;
  upcomingCollections: Array<any>;
}

export interface MemberDashboardData {
  financialSummary: {
    savingsBalance: number;
    totalSavings: number;
    activeLoanAmount: number;
    totalLoanAmount: number;
    nextPaymentAmount: number;
    nextPaymentDate: string;
    creditScore: number;
  };
  activeLoans: Array<any>;
  recentPayments: Array<any>;
  savingsAccounts: Array<any>;
  quickActions: Array<any>;
}

export class DashboardService {
  
  /**
   * Get Admin Dashboard Data
   */
  async getAdminDashboardData(): Promise<AdminDashboardData> {
    try {
      // Get basic metrics
      const [
        totalBranches,
        totalUsers,
        totalFieldOfficers,
        totalMembers,
        totalActiveLoans,
        totalLoanAmount,
        totalCollections
      ] = await Promise.all([
        prisma.branch.count(),
        prisma.user.count(),
        prisma.user.count({ where: { role: UserRole.field_officer } }),
        prisma.member.count(),
        prisma.loan.count({ where: { status: 'ACTIVE' } }),
        prisma.loan.aggregate({
          where: { status: 'ACTIVE' },
          _sum: { loanAmount: true }
        }),
        prisma.installment.aggregate({
          where: { status: InstallmentStatus.COLLECTED },
          _sum: { amount: true }
        })
      ]);

      // Get financial performance (last 3 months)
      const financialPerformance = await this.getFinancialPerformance();

      // Get branch performance
      const branchPerformance = await this.getBranchPerformance();

      // Get recent activities
      const recentActivities = await this.getRecentActivities('', UserRole.admin, '', 10);

      return {
        metrics: {
          totalBranches,
          totalUsers,
          totalFieldOfficers,
          totalMembers,
          totalActiveLoans,
          totalLoanAmount: totalLoanAmount._sum.loanAmount || 0,
          totalCollections: totalCollections._sum.amount || 0,
          systemUptime: '99.8%' // This would come from monitoring service
        },
        financialPerformance,
        branchPerformance,
        recentActivities,
        quickActions: [
          { id: '1', label: 'User Management', icon: 'users', href: '/admin/users', color: 'primary' },
          { id: '2', label: 'Branch Management', icon: 'building', href: '/admin/branches', color: 'success' },
          { id: '3', label: 'Finance Management', icon: 'dollar-sign', href: '/admin/finance', color: 'warning' },
          { id: '4', label: 'Analysis & Reporting', icon: 'bar-chart', href: '/admin/reports', color: 'info' }
        ],
        alerts: []
      };
    } catch (error) {
      logger.error('Error getting admin dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get Manager Dashboard Data
   */
  async getManagerDashboardData(userId: string, branchId: string): Promise<ManagerDashboardData> {
    try {
      // Get branch-specific metrics
      const [
        totalFieldOfficers,
        totalActiveLoans,
        totalMembers,
        collectedAmount,
        pendingInstallments,
        overdueInstallments
      ] = await Promise.all([
        prisma.user.count({ 
          where: { 
            branchId, 
            role: UserRole.field_officer 
          } 
        }),
        prisma.loan.count({ 
          where: { 
            member: { branchId },
            status: 'ACTIVE' 
          } 
        }),
        prisma.member.count({ where: { branchId } }),
        prisma.installment.aggregate({
          where: {
            loan: { member: { branchId } },
            status: InstallmentStatus.COLLECTED,
            collectedAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          },
          _sum: { amount: true }
        }),
        prisma.installment.count({
          where: {
            loan: { member: { branchId } },
            status: InstallmentStatus.PENDING
          }
        }),
        prisma.installment.count({
          where: {
            loan: { member: { branchId } },
            status: InstallmentStatus.OVERDUE
          }
        })
      ]);

      // Get installment status
      const collectedInstallments = await prisma.installment.count({
        where: {
          loan: { member: { branchId } },
          status: InstallmentStatus.COLLECTED,
          collectedAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      });

      // Get field officer performance
      const fieldOfficerPerformance = await this.getFieldOfficerPerformance(branchId);

      // Get recent activities
      const recentActivities = await this.getRecentActivities(userId, UserRole.manager, branchId, 10);

      return {
        metrics: {
          totalFieldOfficers,
          totalActiveLoans,
          totalMembers,
          monthlyCollectionTarget: 1200000, // This should come from branch settings
          collectedAmount: collectedAmount._sum.amount || 0,
          pendingInstallments,
          overdueInstallments,
          branchIncome: collectedAmount._sum.amount || 0,
          branchExpenses: 280000 // This should come from expense tracking
        },
        installmentStatus: {
          pending: pendingInstallments,
          overdue: overdueInstallments,
          collected: collectedInstallments
        },
        fieldOfficerPerformance,
        recentActivities,
        quickActions: [
          { id: '1', label: 'Loan Approval Dashboard', icon: 'check-circle', href: '/loans/approvals', color: 'success' },
          { id: '2', label: 'Branch Income/Expenditure', icon: 'trending-up', href: '/finance/branch', color: 'primary' },
          { id: '3', label: 'Member Registration', icon: 'user-plus', href: '/members/register', color: 'info' },
          { id: '4', label: 'Loan Calculator', icon: 'calculator', href: '/calculator', color: 'secondary' }
        ]
      };
    } catch (error) {
      logger.error('Error getting manager dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get Field Officer Dashboard Data
   */
  async getFieldOfficerDashboardData(userId: string, branchId: string): Promise<FieldOfficerDashboardData> {
    try {
      // Get field officer specific metrics
      const [
        totalMembers,
        totalCollections,
        pendingInstallments,
        overdueInstallments
      ] = await Promise.all([
        prisma.member.count({ 
          where: { 
            branchId,
            createdBy: userId 
          } 
        }),
        prisma.installment.aggregate({
          where: {
            loan: { member: { branchId } },
            collectedBy: userId,
            status: InstallmentStatus.COLLECTED,
            collectedAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          },
          _sum: { amount: true }
        }),
        prisma.installment.count({
          where: {
            loan: { member: { branchId, createdBy: userId } },
            status: InstallmentStatus.PENDING
          }
        }),
        prisma.installment.count({
          where: {
            loan: { member: { branchId, createdBy: userId } },
            status: InstallmentStatus.OVERDUE
          }
        })
      ]);

      const monthlyTarget = 150000; // This should come from user settings
      const achievementPercentage = ((totalCollections._sum.amount || 0) / monthlyTarget) * 100;

      // Get upcoming collections
      const upcomingCollections = await this.getUpcomingCollections(userId, UserRole.field_officer, branchId);

      // Get recent activities
      const recentActivities = await this.getRecentActivities(userId, UserRole.field_officer, branchId, 10);

      return {
        metrics: {
          totalMembers,
          totalCollections: totalCollections._sum.amount || 0,
          monthlyTarget,
          achievementPercentage,
          pendingInstallments,
          overdueInstallments
        },
        recentActivities,
        quickActions: [
          { id: '1', label: 'Member Registration', icon: 'user-plus', href: '/members/register', color: 'primary' },
          { id: '2', label: 'Loan Application', icon: 'file-text', href: '/loans/apply', color: 'success' },
          { id: '3', label: 'Installment Collection', icon: 'credit-card', href: '/collections', color: 'warning' },
          { id: '4', label: 'Loan Calculator', icon: 'calculator', href: '/calculator', color: 'info' }
        ],
        upcomingCollections
      };
    } catch (error) {
      logger.error('Error getting field officer dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get Member Dashboard Data
   */
  async getMemberDashboardData(userId: string, memberId: string): Promise<MemberDashboardData> {
    try {
      // Get member's financial data
      const member = await prisma.member.findUnique({
        where: { id: memberId },
        include: {
          loans: {
            where: { status: 'ACTIVE' },
            include: {
              installments: {
                where: { status: { in: [InstallmentStatus.PENDING, InstallmentStatus.OVERDUE] } },
                orderBy: { dueDate: 'asc' },
                take: 1
              }
            }
          },
          savingsAccounts: true
        }
      });

      if (!member) {
        throw new Error('Member not found');
      }

      // Calculate financial summary
      const totalSavings = member.savingsAccounts.reduce((sum, account) => sum + account.balance, 0);
      const activeLoanAmount = member.loans.reduce((sum, loan) => sum + loan.remainingAmount, 0);
      const totalLoanAmount = member.loans.reduce((sum, loan) => sum + loan.loanAmount, 0);

      // Get next payment info
      const nextInstallment = member.loans
        .flatMap(loan => loan.installments)
        .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())[0];

      // Get recent payments
      const recentPayments = await prisma.installment.findMany({
        where: {
          loan: { memberId },
          status: InstallmentStatus.COLLECTED
        },
        orderBy: { collectedAt: 'desc' },
        take: 10,
        include: {
          loan: true
        }
      });

      return {
        financialSummary: {
          savingsBalance: totalSavings,
          totalSavings,
          activeLoanAmount,
          totalLoanAmount,
          nextPaymentAmount: nextInstallment?.amount || 0,
          nextPaymentDate: nextInstallment?.dueDate.toISOString() || '',
          creditScore: 750 // This would be calculated based on payment history
        },
        activeLoans: member.loans.map(loan => ({
          id: loan.id,
          loanAmount: loan.loanAmount,
          remainingAmount: loan.remainingAmount,
          installmentAmount: loan.installmentAmount,
          nextInstallmentDate: nextInstallment?.dueDate.toISOString() || '',
          totalInstallments: loan.totalInstallments,
          paidInstallments: loan.totalInstallments - Math.ceil(loan.remainingAmount / loan.installmentAmount),
          status: loan.status.toLowerCase()
        })),
        recentPayments: recentPayments.map(payment => ({
          id: payment.id,
          date: payment.collectedAt?.toISOString() || '',
          amount: payment.amount,
          type: 'installment',
          status: 'completed',
          description: `Loan installment for ${payment.loan.loanAmount}`
        })),
        savingsAccounts: member.savingsAccounts.map(account => ({
          id: account.id,
          type: account.accountType.toLowerCase(),
          balance: account.balance,
          interestRate: account.interestRate || 6.5
        })),
        quickActions: [
          { id: '1', label: 'Make Payment', icon: 'credit-card', href: '/payments', color: 'primary' },
          { id: '2', label: 'Apply for Loan', icon: 'file-text', href: '/loans/apply', color: 'success' },
          { id: '3', label: 'View Statements', icon: 'file', href: '/statements', color: 'info' },
          { id: '4', label: 'Contact Support', icon: 'phone', href: '/support', color: 'secondary' }
        ]
      };
    } catch (error) {
      logger.error('Error getting member dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get Recent Activities
   */
  async getRecentActivities(userId: string, userRole: UserRole, branchId?: string, limit: number = 10): Promise<any[]> {
    try {
      // This is a simplified version - in a real app, you'd have an activities/audit log table
      const activities = [];

      // Get recent loan applications
      const recentApplications = await prisma.loanApplication.findMany({
        where: branchId ? { member: { branchId } } : {},
        orderBy: { createdAt: 'desc' },
        take: Math.ceil(limit / 3),
        include: {
          member: true,
          createdByUser: true
        }
      });

      activities.push(...recentApplications.map(app => ({
        id: app.id,
        type: 'loan_application',
        title: 'New Loan Application',
        description: `${app.member.name} applied for ৳${app.appliedAmount.toLocaleString()} loan`,
        timestamp: app.createdAt.toISOString(),
        user: app.createdByUser.name,
        amount: app.appliedAmount,
        status: app.status.toLowerCase()
      })));

      // Get recent installment collections
      const recentCollections = await prisma.installment.findMany({
        where: {
          status: InstallmentStatus.COLLECTED,
          ...(branchId ? { loan: { member: { branchId } } } : {})
        },
        orderBy: { collectedAt: 'desc' },
        take: Math.ceil(limit / 3),
        include: {
          loan: {
            include: {
              member: true
            }
          },
          collectedByUser: true
        }
      });

      activities.push(...recentCollections.map(collection => ({
        id: collection.id,
        type: 'installment_collection',
        title: 'Installment Collected',
        description: `Collected ৳${collection.amount.toLocaleString()} from ${collection.loan.member.name}`,
        timestamp: collection.collectedAt?.toISOString() || '',
        user: collection.collectedByUser?.name || 'System',
        amount: collection.amount,
        status: 'completed'
      })));

      // Get recent member registrations
      const recentMembers = await prisma.member.findMany({
        where: branchId ? { branchId } : {},
        orderBy: { createdAt: 'desc' },
        take: Math.ceil(limit / 3),
        include: {
          createdByUser: true
        }
      });

      activities.push(...recentMembers.map(member => ({
        id: member.id,
        type: 'member_registration',
        title: 'New Member Registered',
        description: `${member.name} registered as new member`,
        timestamp: member.createdAt.toISOString(),
        user: member.createdByUser?.name || 'System',
        status: 'completed'
      })));

      // Sort by timestamp and limit
      return activities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit);
    } catch (error) {
      logger.error('Error getting recent activities:', error);
      return [];
    }
  }

  /**
   * Get Upcoming Collections
   */
  async getUpcomingCollections(userId: string, userRole: UserRole, branchId?: string): Promise<any[]> {
    try {
      const whereClause: any = {
        status: { in: [InstallmentStatus.PENDING, InstallmentStatus.OVERDUE] },
        dueDate: {
          lte: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Next 7 days
        }
      };

      if (userRole === UserRole.field_officer) {
        whereClause.loan = {
          member: {
            branchId,
            createdBy: userId
          }
        };
      } else if (branchId) {
        whereClause.loan = {
          member: { branchId }
        };
      }

      const upcomingInstallments = await prisma.installment.findMany({
        where: whereClause,
        orderBy: { dueDate: 'asc' },
        take: 20,
        include: {
          loan: {
            include: {
              member: true
            }
          }
        }
      });

      return upcomingInstallments.map(installment => ({
        id: installment.id,
        memberName: installment.loan.member.name,
        memberId: installment.loan.member.memberId,
        amount: installment.amount,
        dueDate: installment.dueDate.toISOString(),
        status: installment.status.toLowerCase(),
        loanId: installment.loanId,
        installmentNo: installment.installmentNumber
      }));
    } catch (error) {
      logger.error('Error getting upcoming collections:', error);
      return [];
    }
  }

  // Helper methods
  private async getFinancialPerformance(): Promise<any[]> {
    // This would typically involve complex queries to get monthly data
    // For now, returning mock data structure
    return [
      { period: 'Jan', collections: 1200000, disbursements: 2800000, members: 2650 },
      { period: 'Feb', collections: 1350000, disbursements: 2200000, members: 2720 },
      { period: 'Mar', collections: 1280000, disbursements: 2600000, members: 2847 }
    ];
  }

  private async getBranchPerformance(): Promise<any[]> {
    // This would involve complex aggregations across branches
    // For now, returning mock data structure
    return [
      { 
        id: '1', 
        name: 'Dhaka Main Branch', 
        managerId: '1', 
        managerName: 'Karim Ahmed', 
        totalMembers: 342, 
        activeLoans: 156, 
        collectionsThisMonth: 980000, 
        performance: 87.5 
      }
    ];
  }

  private async getFieldOfficerPerformance(branchId: string): Promise<any[]> {
    // This would involve complex aggregations for field officers
    // For now, returning mock data structure
    return [
      { id: '1', name: 'Rahul Ahmed', membersCount: 45, collectionsThisMonth: 125000, targetAchievement: 83.3 },
      { id: '2', name: 'Fatima Khan', membersCount: 52, collectionsThisMonth: 145000, targetAchievement: 96.7 }
    ];
  }
}

export const dashboardService = new DashboardService();
