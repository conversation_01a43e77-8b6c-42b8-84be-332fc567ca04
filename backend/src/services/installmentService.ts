import { prisma } from '@/config/database';
import { Installment, InstallmentStatus, UserRole } from '@prisma/client';
import { logger } from '@/utils/logger';

export interface InstallmentListQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: InstallmentStatus;
  branchId?: string;
  memberId?: string;
  loanId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CollectInstallmentData {
  collectedAmount: number;
  collectionDate?: Date;
  collectorId: string;
  notes?: string;
}

export interface InstallmentCollection {
  id: string;
  installmentId: string;
  collectedAmount: number;
  collectionDate: Date;
  collectorId: string;
  notes?: string;
  createdAt: Date;
}

export class InstallmentService {
  async getInstallments(query: InstallmentListQuery, userRole: UserRole, userBranchId?: string): Promise<{
    installments: (Installment & { loan?: any; collections?: any[] })[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status,
        branchId,
        memberId,
        loanId,
        sortBy = 'installmentDate',
        sortOrder = 'asc',
      } = query;

      const skip = (page - 1) * limit;
      const where: any = {};

      // Role-based filtering
      if (userRole !== UserRole.admin) {
        where.loan = {
          loanApplication: {
            member: { branchId: userBranchId }
          }
        };
      } else if (branchId) {
        where.loan = {
          loanApplication: {
            member: { branchId }
          }
        };
      }

      // Add loan filter
      if (loanId) {
        where.loanId = loanId;
      }

      // Add member filter
      if (memberId) {
        where.loan = {
          ...where.loan,
          loanApplication: {
            ...where.loan?.loanApplication,
            memberId
          }
        };
      }

      // Add status filter
      if (status) {
        where.status = status;
      }

      // Add search filter
      if (search) {
        where.loan = {
          ...where.loan,
          loanApplication: {
            ...where.loan?.loanApplication,
            member: {
              ...where.loan?.loanApplication?.member,
              OR: [
                { name: { contains: search, mode: 'insensitive' } },
                { memberId: { contains: search, mode: 'insensitive' } },
              ],
            },
          },
        };
      }

      const [installments, total] = await Promise.all([
        prisma.installment.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            loan: {
              include: {
                loanApplication: {
                  include: {
                    member: {
                      select: {
                        id: true,
                        memberId: true,
                        name: true,
                        phoneNumber: true,
                        branch: {
                          select: {
                            id: true,
                            name: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        }),
        prisma.installment.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        installments,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get installments error:', error);
      throw error;
    }
  }

  async getPendingInstallments(query: InstallmentListQuery, userRole: UserRole, userBranchId?: string): Promise<{
    installments: (Installment & { loan?: any })[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const pendingQuery = {
        ...query,
        status: InstallmentStatus.pending,
      };

      return await this.getInstallments(pendingQuery, userRole, userBranchId);
    } catch (error) {
      logger.error('Get pending installments error:', error);
      throw error;
    }
  }

  async getOverdueInstallments(query: InstallmentListQuery, userRole: UserRole, userBranchId?: string): Promise<{
    installments: (Installment & { loan?: any })[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      // First update overdue status
      await this.updateOverdueInstallments();

      const overdueQuery = {
        ...query,
        status: InstallmentStatus.overdue,
      };

      return await this.getInstallments(overdueQuery, userRole, userBranchId);
    } catch (error) {
      logger.error('Get overdue installments error:', error);
      throw error;
    }
  }

  async collectInstallment(installmentId: string, data: CollectInstallmentData, userRole: UserRole, userBranchId?: string): Promise<{
    installment: Installment;
    collection: any;
  }> {
    try {
      // Check if installment exists and user has access
      const installment = await prisma.installment.findUnique({
        where: { id: installmentId },
        include: {
          loan: {
            include: {
              loanApplication: {
                include: {
                  member: {
                    select: {
                      branchId: true,
                      name: true,
                      memberId: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!installment) {
        throw new Error('Installment not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && installment.loan.loanApplication.member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only collect installments from your own branch');
      }

      // Check if installment can be collected
      if (installment.status === InstallmentStatus.paid) {
        throw new Error('Installment has already been paid');
      }

      // Validate collection amount
      if (data.collectedAmount <= 0) {
        throw new Error('Collection amount must be greater than zero');
      }

      if (data.collectedAmount > installment.installmentAmount) {
        throw new Error('Collection amount cannot exceed installment amount');
      }

      const collectionDate = data.collectionDate || new Date();

      // Create collection record and update installment
      const [collection, updatedInstallment] = await prisma.$transaction(async (tx) => {
        // Create collection record
        const newCollection = await tx.installmentCollection.create({
          data: {
            installmentId,
            collectedAmount: data.collectedAmount,
            collectionDate,
            collectorId: data.collectorId,
            notes: data.notes,
          },
        });

        // Calculate total collected amount
        const totalCollected = await tx.installmentCollection.aggregate({
          where: { installmentId },
          _sum: { collectedAmount: true },
        });

        const totalCollectedAmount = totalCollected._sum.collectedAmount || 0;

        // Update installment status
        let newStatus = InstallmentStatus.partial;
        if (totalCollectedAmount >= installment.installmentAmount) {
          newStatus = InstallmentStatus.paid;
        }

        const updatedInstallment = await tx.installment.update({
          where: { id: installmentId },
          data: {
            status: newStatus,
            collectionDate: newStatus === InstallmentStatus.paid ? collectionDate : installment.collectionDate,
          },
          include: {
            loan: {
              include: {
                loanApplication: {
                  include: {
                    member: {
                      select: {
                        id: true,
                        memberId: true,
                        name: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        return [newCollection, updatedInstallment];
      });

      logger.info('Installment collected', {
        installmentId,
        collectedAmount: data.collectedAmount,
        collectorId: data.collectorId,
        memberName: installment.loan.loanApplication.member.name,
        memberId: installment.loan.loanApplication.member.memberId,
      });

      return { installment: updatedInstallment, collection };
    } catch (error) {
      logger.error('Collect installment error:', error);
      throw error;
    }
  }

  async getInstallmentHistory(installmentId: string, userRole: UserRole, userBranchId?: string): Promise<any[]> {
    try {
      // Check if installment exists and user has access
      const installment = await prisma.installment.findUnique({
        where: { id: installmentId },
        include: {
          loan: {
            include: {
              loanApplication: {
                include: {
                  member: {
                    select: {
                      branchId: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!installment) {
        throw new Error('Installment not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && installment.loan.loanApplication.member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only view installment history from your own branch');
      }

      const collections = await prisma.installmentCollection.findMany({
        where: { installmentId },
        include: {
          collector: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { collectionDate: 'desc' },
      });

      return collections;
    } catch (error) {
      logger.error('Get installment history error:', error);
      throw error;
    }
  }

  async getMemberInstallments(memberId: string, userRole: UserRole, userBranchId?: string): Promise<Installment[]> {
    try {
      // Check if member exists and user has access
      const member = await prisma.member.findUnique({
        where: { id: memberId },
        select: { id: true, branchId: true },
      });

      if (!member) {
        throw new Error('Member not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only view installments from your own branch');
      }

      const installments = await prisma.installment.findMany({
        where: {
          loan: {
            loanApplication: {
              memberId,
            },
          },
        },
        include: {
          loan: {
            select: {
              id: true,
              loanAmount: true,
              loanDate: true,
            },
          },
        },
        orderBy: { installmentDate: 'asc' },
      });

      return installments;
    } catch (error) {
      logger.error('Get member installments error:', error);
      throw error;
    }
  }

  async updateInstallmentStatus(installmentId: string, status: InstallmentStatus, userRole: UserRole, userBranchId?: string): Promise<Installment> {
    try {
      // Check if installment exists and user has access
      const installment = await prisma.installment.findUnique({
        where: { id: installmentId },
        include: {
          loan: {
            include: {
              loanApplication: {
                include: {
                  member: {
                    select: {
                      branchId: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!installment) {
        throw new Error('Installment not found');
      }

      // Role-based access control
      if (userRole !== UserRole.admin && installment.loan.loanApplication.member.branchId !== userBranchId) {
        throw new Error('Access denied: You can only update installments from your own branch');
      }

      const updatedInstallment = await prisma.installment.update({
        where: { id: installmentId },
        data: { status },
        include: {
          loan: {
            include: {
              loanApplication: {
                include: {
                  member: {
                    select: {
                      id: true,
                      memberId: true,
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      logger.info('Installment status updated', {
        installmentId,
        newStatus: status,
        oldStatus: installment.status,
      });

      return updatedInstallment;
    } catch (error) {
      logger.error('Update installment status error:', error);
      throw error;
    }
  }

  async updateOverdueInstallments(): Promise<number> {
    try {
      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today

      const result = await prisma.installment.updateMany({
        where: {
          installmentDate: {
            lt: today,
          },
          status: InstallmentStatus.pending,
        },
        data: {
          status: InstallmentStatus.overdue,
        },
      });

      if (result.count > 0) {
        logger.info('Updated overdue installments', { count: result.count });
      }

      return result.count;
    } catch (error) {
      logger.error('Update overdue installments error:', error);
      throw error;
    }
  }
}

export const installmentService = new InstallmentService();
