import { PrismaClient, User, UserRole } from '@prisma/client';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';
import bcrypt from 'bcryptjs';
import { config } from '@/config';

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  role: UserRole;
  memberId?: string;
  branchId?: string;
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  isActive?: boolean;
  role?: UserRole;
  memberId?: string;
  branchId?: string;
}

export interface UserListQuery {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  branchId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export class PrismaUserService {
  async createUser(data: CreateUserData): Promise<User> {
    try {
      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email }
      });

      if (existingUser) {
        throw new Error('User with this email already exists');
      }

      // Check if memberId is unique if provided
      if (data.memberId) {
        const existingMemberId = await prisma.user.findUnique({
          where: { memberId: data.memberId }
        });

        if (existingMemberId) {
          throw new Error('Member ID already exists');
        }
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, config.bcrypt.saltRounds);

      const user = await prisma.user.create({
        data: {
          ...data,
          password: hashedPassword,
        },
        include: {
          branch: true,
          managedBranches: true,
        }
      });

      logger.info('User created successfully', { userId: user.id, email: user.email });
      return user;
    } catch (error) {
      logger.error('Create user error:', error);
      throw error;
    }
  }

  async findUserByEmail(email: string): Promise<User | null> {
    try {
      return await prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        include: {
          branch: true,
          managedBranches: true,
        }
      });
    } catch (error) {
      logger.error('Find user by email error:', error);
      throw error;
    }
  }

  async findUserByMemberId(memberId: string): Promise<User | null> {
    try {
      return await prisma.user.findUnique({
        where: { memberId: memberId.toUpperCase() },
        include: {
          branch: true,
          managedBranches: true,
        }
      });
    } catch (error) {
      logger.error('Find user by member ID error:', error);
      throw error;
    }
  }

  async findUserByEmailOrMemberId(identifier: string): Promise<User | null> {
    try {
      // Try to find by email first
      let user = await this.findUserByEmail(identifier);

      // If not found and identifier doesn't contain @, try member ID
      if (!user && !identifier.includes('@')) {
        user = await this.findUserByMemberId(identifier);
      }

      return user;
    } catch (error) {
      logger.error('Find user by email or member ID error:', error);
      throw error;
    }
  }

  async findUserById(id: string): Promise<User | null> {
    try {
      return await prisma.user.findUnique({
        where: { id },
        include: {
          branch: true,
          managedBranches: true,
        }
      });
    } catch (error) {
      logger.error('Find user by ID error:', error);
      throw error;
    }
  }

  async updateUser(id: string, data: UpdateUserData): Promise<User> {
    try {
      // Check if email is being changed and if it's already taken
      if (data.email) {
        const existingUser = await prisma.user.findFirst({
          where: {
            email: data.email,
            NOT: { id }
          }
        });

        if (existingUser) {
          throw new Error('Email is already taken');
        }
      }

      // Check if memberId is being changed and if it's already taken
      if (data.memberId) {
        const existingMemberId = await prisma.user.findFirst({
          where: {
            memberId: data.memberId,
            NOT: { id }
          }
        });

        if (existingMemberId) {
          throw new Error('Member ID is already taken');
        }
      }

      const user = await prisma.user.update({
        where: { id },
        data,
        include: {
          branch: true,
          managedBranches: true,
        }
      });

      logger.info('User updated successfully', { userId: user.id });
      return user;
    } catch (error) {
      logger.error('Update user error:', error);
      throw error;
    }
  }

  async updateLastLogin(id: string): Promise<void> {
    try {
      await prisma.user.update({
        where: { id },
        data: { lastLoginAt: new Date() }
      });
    } catch (error) {
      logger.error('Update last login error:', error);
      throw error;
    }
  }

  async changePassword(id: string, newPassword: string): Promise<void> {
    try {
      const hashedPassword = await bcrypt.hash(newPassword, config.bcrypt.saltRounds);
      
      await prisma.user.update({
        where: { id },
        data: { password: hashedPassword }
      });

      logger.info('Password changed successfully', { userId: id });
    } catch (error) {
      logger.error('Change password error:', error);
      throw error;
    }
  }

  async comparePassword(user: User, candidatePassword: string): Promise<boolean> {
    try {
      return await bcrypt.compare(candidatePassword, user.password);
    } catch (error) {
      logger.error('Compare password error:', error);
      return false;
    }
  }

  async getUsers(query: UserListQuery) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        role,
        isActive,
        branchId,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;
      const where: any = {};

      // Add search filter
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { memberId: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Add role filter
      if (role) {
        where.role = role;
      }

      // Add active status filter
      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      // Add branch filter
      if (branchId) {
        where.branchId = branchId;
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            branch: true,
            managedBranches: true,
          }
        }),
        prisma.user.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get users error:', error);
      throw error;
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      await prisma.user.delete({
        where: { id }
      });

      logger.info('User deleted successfully', { userId: id });
    } catch (error) {
      logger.error('Delete user error:', error);
      throw error;
    }
  }
}

export const prismaUserService = new PrismaUserService();
