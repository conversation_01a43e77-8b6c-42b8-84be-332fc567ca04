import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';

export interface LoginAttemptData {
  userId?: string;
  memberId?: string;
  ipAddress: string;
  userAgent: string;
  successful: boolean;
  failureReason?: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remainingAttempts: number;
  resetTime: Date;
  lockoutDuration?: number; // in minutes
}

export class RateLimitService {
  private readonly MAX_ATTEMPTS_PER_IP = 10; // per hour
  private readonly MAX_ATTEMPTS_PER_USER = 5; // per 15 minutes
  private readonly LOCKOUT_DURATION = 15; // minutes
  private readonly PROGRESSIVE_LOCKOUT_MULTIPLIER = 2;

  async recordLoginAttempt(data: LoginAttemptData): Promise<void> {
    try {
      await prisma.loginAttempt.create({
        data: {
          userId: data.userId,
          memberId: data.memberId,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          successful: data.successful,
          failureReason: data.failureReason,
        },
      });

      // Update user failed attempts counter if login failed
      if (!data.successful && data.userId) {
        await this.updateUserFailedAttempts(data.userId);
      }

      logger.info('Login attempt recorded', {
        userId: data.userId,
        memberId: data.memberId,
        ipAddress: data.ipAddress,
        successful: data.successful,
      });
    } catch (error) {
      logger.error('Error recording login attempt:', error);
    }
  }

  async checkRateLimit(ipAddress: string, userId?: string): Promise<RateLimitResult> {
    try {
      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000);

      // Check IP-based rate limiting
      const ipAttempts = await prisma.loginAttempt.count({
        where: {
          ipAddress,
          attemptedAt: { gte: oneHourAgo },
          successful: false,
        },
      });

      if (ipAttempts >= this.MAX_ATTEMPTS_PER_IP) {
        return {
          allowed: false,
          remainingAttempts: 0,
          resetTime: new Date(now.getTime() + 60 * 60 * 1000), // 1 hour
          lockoutDuration: 60,
        };
      }

      // Check user-based rate limiting if userId provided
      if (userId) {
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { 
            failedLoginAttempts: true, 
            lockedUntil: true 
          },
        });

        if (user) {
          // Check if user is currently locked
          if (user.lockedUntil && user.lockedUntil > now) {
            const lockoutMinutes = Math.ceil((user.lockedUntil.getTime() - now.getTime()) / (1000 * 60));
            return {
              allowed: false,
              remainingAttempts: 0,
              resetTime: user.lockedUntil,
              lockoutDuration: lockoutMinutes,
            };
          }

          // Check recent failed attempts
          const userAttempts = await prisma.loginAttempt.count({
            where: {
              userId,
              attemptedAt: { gte: fifteenMinutesAgo },
              successful: false,
            },
          });

          if (userAttempts >= this.MAX_ATTEMPTS_PER_USER) {
            // Lock the user account
            const lockoutDuration = this.calculateLockoutDuration(user.failedLoginAttempts);
            const lockedUntil = new Date(now.getTime() + lockoutDuration * 60 * 1000);

            await prisma.user.update({
              where: { id: userId },
              data: { lockedUntil },
            });

            return {
              allowed: false,
              remainingAttempts: 0,
              resetTime: lockedUntil,
              lockoutDuration,
            };
          }

          return {
            allowed: true,
            remainingAttempts: this.MAX_ATTEMPTS_PER_USER - userAttempts,
            resetTime: new Date(now.getTime() + 15 * 60 * 1000),
          };
        }
      }

      return {
        allowed: true,
        remainingAttempts: this.MAX_ATTEMPTS_PER_IP - ipAttempts,
        resetTime: new Date(now.getTime() + 60 * 60 * 1000),
      };
    } catch (error) {
      logger.error('Error checking rate limit:', error);
      // Fail open for availability
      return {
        allowed: true,
        remainingAttempts: 1,
        resetTime: new Date(),
      };
    }
  }

  private async updateUserFailedAttempts(userId: string): Promise<void> {
    try {
      await prisma.user.update({
        where: { id: userId },
        data: {
          failedLoginAttempts: { increment: 1 },
        },
      });
    } catch (error) {
      logger.error('Error updating user failed attempts:', error);
    }
  }

  private calculateLockoutDuration(failedAttempts: number): number {
    // Progressive lockout: 15, 30, 60, 120, 240 minutes
    const baseDuration = this.LOCKOUT_DURATION;
    const multiplier = Math.min(Math.pow(this.PROGRESSIVE_LOCKOUT_MULTIPLIER, Math.floor(failedAttempts / 5)), 16);
    return baseDuration * multiplier;
  }

  async resetUserFailedAttempts(userId: string): Promise<void> {
    try {
      await prisma.user.update({
        where: { id: userId },
        data: {
          failedLoginAttempts: 0,
          lockedUntil: null,
        },
      });

      logger.info('User failed attempts reset', { userId });
    } catch (error) {
      logger.error('Error resetting user failed attempts:', error);
    }
  }

  async getLoginAttemptStats(timeframe: 'hour' | 'day' | 'week' = 'day'): Promise<{
    totalAttempts: number;
    successfulAttempts: number;
    failedAttempts: number;
    uniqueIPs: number;
    topFailureReasons: Array<{ reason: string; count: number }>;
  }> {
    try {
      const now = new Date();
      let startTime: Date;

      switch (timeframe) {
        case 'hour':
          startTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case 'week':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        default:
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }

      const [totalAttempts, successfulAttempts, failedAttempts, uniqueIPs, failureReasons] = await Promise.all([
        prisma.loginAttempt.count({
          where: { attemptedAt: { gte: startTime } },
        }),
        prisma.loginAttempt.count({
          where: { 
            attemptedAt: { gte: startTime },
            successful: true,
          },
        }),
        prisma.loginAttempt.count({
          where: { 
            attemptedAt: { gte: startTime },
            successful: false,
          },
        }),
        prisma.loginAttempt.findMany({
          where: { attemptedAt: { gte: startTime } },
          select: { ipAddress: true },
          distinct: ['ipAddress'],
        }),
        prisma.loginAttempt.groupBy({
          by: ['failureReason'],
          where: {
            attemptedAt: { gte: startTime },
            successful: false,
            failureReason: { not: null },
          },
          _count: { failureReason: true },
          orderBy: { _count: { failureReason: 'desc' } },
          take: 5,
        }),
      ]);

      return {
        totalAttempts,
        successfulAttempts,
        failedAttempts,
        uniqueIPs: uniqueIPs.length,
        topFailureReasons: failureReasons.map(fr => ({
          reason: fr.failureReason || 'Unknown',
          count: fr._count.failureReason,
        })),
      };
    } catch (error) {
      logger.error('Error getting login attempt stats:', error);
      return {
        totalAttempts: 0,
        successfulAttempts: 0,
        failedAttempts: 0,
        uniqueIPs: 0,
        topFailureReasons: [],
      };
    }
  }

  async cleanupOldAttempts(): Promise<void> {
    try {
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      
      const result = await prisma.loginAttempt.deleteMany({
        where: { attemptedAt: { lt: oneWeekAgo } },
      });

      logger.info('Cleaned up old login attempts', { deletedCount: result.count });
    } catch (error) {
      logger.error('Error cleaning up old login attempts:', error);
    }
  }

  async isIPSuspicious(ipAddress: string): Promise<boolean> {
    try {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      const recentFailures = await prisma.loginAttempt.count({
        where: {
          ipAddress,
          attemptedAt: { gte: oneDayAgo },
          successful: false,
        },
      });

      // Consider IP suspicious if more than 20 failed attempts in 24 hours
      return recentFailures > 20;
    } catch (error) {
      logger.error('Error checking IP suspicion:', error);
      return false;
    }
  }
}

export const rateLimitService = new RateLimitService();
