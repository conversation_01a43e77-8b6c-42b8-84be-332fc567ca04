import { prisma } from '@/config/database';
import { User, UserRole } from '@prisma/client';
import { logger } from '@/utils/logger';
import { prismaUserService, CreateUserData } from '@/services/prisma/userService';
import bcrypt from 'bcryptjs';
import { config } from '@/config';
import * as XLSX from 'xlsx';

export interface UserListQuery {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  branchId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedUsers {
  users: Omit<User, 'password'>[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface BulkImportResult {
  success: number;
  failed: number;
  errors: Array<{
    row: number;
    error: string;
    data?: any;
  }>;
}

export class UserManagementService {
  async getUsers(query: UserListQuery): Promise<PaginatedUsers> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        role,
        isActive,
        branchId,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;
      const where: any = {};

      // Add search filter
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { memberId: { contains: search, mode: 'insensitive' } },
        ];
      }

      // Add role filter
      if (role) {
        where.role = role;
      }

      // Add active status filter
      if (typeof isActive === 'boolean') {
        where.isActive = isActive;
      }

      // Add branch filter
      if (branchId) {
        where.branchId = branchId;
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            branch: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            memberId: true,
            isActive: true,
            lastLoginAt: true,
            createdAt: true,
            updatedAt: true,
            branch: true,
          },
        }),
        prisma.user.count({ where })
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        users,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get users error:', error);
      throw error;
    }
  }

  async createUser(data: CreateUserData): Promise<Omit<User, 'password'>> {
    try {
      const user = await prismaUserService.createUser(data);
      
      logger.info('User created by admin', {
        userId: user.id,
        email: user.email,
        role: user.role,
      });

      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      logger.error('Create user error:', error);
      throw error;
    }
  }

  async getUserById(id: string): Promise<Omit<User, 'password'> | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          branch: {
            select: {
              id: true,
              name: true,
              address: true,
            },
          },
          managedBranches: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          memberId: true,
          isActive: true,
          lastLoginAt: true,
          failedLoginAttempts: true,
          lockedUntil: true,
          createdAt: true,
          updatedAt: true,
          branch: true,
          managedBranches: true,
        },
      });

      return user;
    } catch (error) {
      logger.error('Get user by ID error:', error);
      throw error;
    }
  }

  async updateUser(id: string, data: Partial<CreateUserData>): Promise<Omit<User, 'password'>> {
    try {
      const user = await prismaUserService.updateUser(id, data);
      
      logger.info('User updated by admin', {
        userId: user.id,
        updatedFields: Object.keys(data),
      });

      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      logger.error('Update user error:', error);
      throw error;
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { id },
        select: { id: true, email: true, role: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Prevent deletion of admin users (optional business rule)
      if (user.role === UserRole.admin) {
        throw new Error('Cannot delete admin users');
      }

      await prisma.user.delete({
        where: { id }
      });

      logger.info('User deleted by admin', {
        deletedUserId: id,
        deletedUserEmail: user.email,
      });
    } catch (error) {
      logger.error('Delete user error:', error);
      throw error;
    }
  }

  async resetUserPassword(id: string, newPassword: string): Promise<void> {
    try {
      await prismaUserService.changePassword(id, newPassword);
      
      // Reset failed login attempts and unlock account
      await prisma.user.update({
        where: { id },
        data: {
          failedLoginAttempts: 0,
          lockedUntil: null,
        },
      });

      logger.info('User password reset by admin', { userId: id });
    } catch (error) {
      logger.error('Reset user password error:', error);
      throw error;
    }
  }

  async toggleUserStatus(id: string): Promise<Omit<User, 'password'>> {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
        select: { id: true, isActive: true, email: true },
      });

      if (!user) {
        throw new Error('User not found');
      }

      const updatedUser = await prisma.user.update({
        where: { id },
        data: { isActive: !user.isActive },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          memberId: true,
          isActive: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      logger.info('User status toggled by admin', {
        userId: id,
        newStatus: updatedUser.isActive,
      });

      return updatedUser;
    } catch (error) {
      logger.error('Toggle user status error:', error);
      throw error;
    }
  }

  async bulkImportUsers(usersData: any[]): Promise<BulkImportResult> {
    const result: BulkImportResult = {
      success: 0,
      failed: 0,
      errors: [],
    };

    try {
      for (let i = 0; i < usersData.length; i++) {
        const userData = usersData[i];
        const rowNumber = i + 1;

        try {
          // Validate required fields
          if (!userData.name || !userData.email || !userData.password) {
            throw new Error('Missing required fields: name, email, password');
          }

          // Create user
          await this.createUser({
            name: userData.name,
            email: userData.email,
            password: userData.password,
            role: userData.role || UserRole.member,
            memberId: userData.memberId,
            branchId: userData.branchId,
          });

          result.success++;
        } catch (error) {
          result.failed++;
          result.errors.push({
            row: rowNumber,
            error: error instanceof Error ? error.message : 'Unknown error',
            data: userData,
          });
        }
      }

      logger.info('Bulk import completed', {
        total: usersData.length,
        success: result.success,
        failed: result.failed,
      });

      return result;
    } catch (error) {
      logger.error('Bulk import error:', error);
      throw error;
    }
  }

  async exportUsers(query: UserListQuery): Promise<Buffer> {
    try {
      // Get all users matching the query (without pagination)
      const { users } = await this.getUsers({
        ...query,
        page: 1,
        limit: 10000, // Large limit to get all users
      });

      // Prepare data for export
      const exportData = users.map(user => ({
        ID: user.id,
        Name: user.name,
        Email: user.email,
        'Member ID': user.memberId || '',
        Role: user.role,
        Status: user.isActive ? 'Active' : 'Inactive',
        Branch: user.branch?.name || '',
        'Last Login': user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : 'Never',
        'Created At': new Date(user.createdAt).toLocaleString(),
      }));

      // Create Excel workbook
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(exportData);

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Users');

      // Generate buffer
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      logger.info('Users exported', { count: users.length });

      return buffer;
    } catch (error) {
      logger.error('Export users error:', error);
      throw error;
    }
  }

  async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<UserRole, number>;
    recentLogins: number;
  }> {
    try {
      const [total, active, inactive, roleStats, recentLogins] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { isActive: true } }),
        prisma.user.count({ where: { isActive: false } }),
        prisma.user.groupBy({
          by: ['role'],
          _count: { role: true },
        }),
        prisma.user.count({
          where: {
            lastLoginAt: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
            },
          },
        }),
      ]);

      const byRole = Object.values(UserRole).reduce((acc, role) => {
        acc[role] = 0;
        return acc;
      }, {} as Record<UserRole, number>);

      roleStats.forEach(stat => {
        byRole[stat.role] = stat._count.role;
      });

      return {
        total,
        active,
        inactive,
        byRole,
        recentLogins,
      };
    } catch (error) {
      logger.error('Get user stats error:', error);
      throw error;
    }
  }
}

export const userManagementService = new UserManagementService();
