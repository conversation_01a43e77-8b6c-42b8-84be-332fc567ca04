import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';
import { config } from '@/config';

export interface CreateSessionData {
  userId: string;
  sessionId: string;
  ipAddress: string;
  userAgent: string;
  rememberMe: boolean;
}

export interface SessionInfo {
  id: string;
  sessionId: string;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  rememberMe: boolean;
  expiresAt: Date;
  lastActivity: Date;
  createdAt: Date;
}

export class SessionService {
  async createSession(data: CreateSessionData): Promise<SessionInfo> {
    try {
      const expiresAt = new Date();
      if (data.rememberMe) {
        // Remember me sessions last 30 days
        expiresAt.setDate(expiresAt.getDate() + 30);
      } else {
        // Regular sessions last 24 hours
        expiresAt.setHours(expiresAt.getHours() + 24);
      }

      const session = await prisma.userSession.create({
        data: {
          userId: data.userId,
          sessionId: data.sessionId,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          rememberMe: data.rememberMe,
          expiresAt,
          isActive: true,
        },
      });

      logger.info('Session created', { 
        userId: data.userId, 
        sessionId: data.sessionId,
        rememberMe: data.rememberMe 
      });

      return session;
    } catch (error) {
      logger.error('Error creating session:', error);
      throw error;
    }
  }

  async getSession(sessionId: string): Promise<SessionInfo | null> {
    try {
      return await prisma.userSession.findUnique({
        where: { sessionId },
      });
    } catch (error) {
      logger.error('Error getting session:', error);
      return null;
    }
  }

  async updateSessionActivity(sessionId: string): Promise<void> {
    try {
      await prisma.userSession.update({
        where: { sessionId },
        data: { lastActivity: new Date() },
      });
    } catch (error) {
      logger.error('Error updating session activity:', error);
    }
  }

  async invalidateSession(sessionId: string): Promise<void> {
    try {
      await prisma.userSession.update({
        where: { sessionId },
        data: { isActive: false },
      });

      logger.info('Session invalidated', { sessionId });
    } catch (error) {
      logger.error('Error invalidating session:', error);
      throw error;
    }
  }

  async invalidateAllUserSessions(userId: string, exceptSessionId?: string): Promise<void> {
    try {
      const where: any = { userId, isActive: true };
      if (exceptSessionId) {
        where.sessionId = { not: exceptSessionId };
      }

      await prisma.userSession.updateMany({
        where,
        data: { isActive: false },
      });

      logger.info('All user sessions invalidated', { userId, exceptSessionId });
    } catch (error) {
      logger.error('Error invalidating user sessions:', error);
      throw error;
    }
  }

  async getUserActiveSessions(userId: string): Promise<SessionInfo[]> {
    try {
      return await prisma.userSession.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
        orderBy: { lastActivity: 'desc' },
      });
    } catch (error) {
      logger.error('Error getting user active sessions:', error);
      return [];
    }
  }

  async cleanupExpiredSessions(): Promise<void> {
    try {
      const result = await prisma.userSession.updateMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { isActive: false },
          ],
        },
        data: { isActive: false },
      });

      logger.info('Cleaned up expired sessions', { updatedCount: result.count });
    } catch (error) {
      logger.error('Error cleaning up expired sessions:', error);
    }
  }

  async isSessionValid(sessionId: string): Promise<boolean> {
    try {
      const session = await prisma.userSession.findUnique({
        where: { sessionId },
      });

      return !!(session && 
        session.isActive && 
        session.expiresAt > new Date());
    } catch (error) {
      logger.error('Error checking session validity:', error);
      return false;
    }
  }

  async getSessionStats(userId: string): Promise<{
    totalSessions: number;
    activeSessions: number;
    lastLoginAt: Date | null;
  }> {
    try {
      const [totalSessions, activeSessions, lastSession] = await Promise.all([
        prisma.userSession.count({ where: { userId } }),
        prisma.userSession.count({ 
          where: { 
            userId, 
            isActive: true, 
            expiresAt: { gt: new Date() } 
          } 
        }),
        prisma.userSession.findFirst({
          where: { userId },
          orderBy: { createdAt: 'desc' },
        }),
      ]);

      return {
        totalSessions,
        activeSessions,
        lastLoginAt: lastSession?.createdAt || null,
      };
    } catch (error) {
      logger.error('Error getting session stats:', error);
      return {
        totalSessions: 0,
        activeSessions: 0,
        lastLoginAt: null,
      };
    }
  }

  // Limit concurrent sessions per user
  async enforceConcurrentSessionLimit(userId: string, maxSessions: number = 5): Promise<void> {
    try {
      const activeSessions = await prisma.userSession.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
        orderBy: { lastActivity: 'asc' },
      });

      if (activeSessions.length >= maxSessions) {
        // Deactivate oldest sessions
        const sessionsToDeactivate = activeSessions.slice(0, activeSessions.length - maxSessions + 1);
        
        await prisma.userSession.updateMany({
          where: {
            id: { in: sessionsToDeactivate.map(s => s.id) },
          },
          data: { isActive: false },
        });

        logger.info('Enforced concurrent session limit', { 
          userId, 
          deactivatedSessions: sessionsToDeactivate.length 
        });
      }
    } catch (error) {
      logger.error('Error enforcing session limit:', error);
    }
  }
}

export const sessionService = new SessionService();
