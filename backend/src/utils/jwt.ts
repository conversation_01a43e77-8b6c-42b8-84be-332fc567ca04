import jwt from 'jsonwebtoken';
import { config } from '@/config';
import { User } from '@prisma/client';
import { tokenBlacklistService } from '@/models/TokenBlacklist';
import { logger } from '@/utils/logger';
import crypto from 'crypto';

export interface JwtPayload {
  userId: string;
  email: string;
  memberId?: string;
  role: string;
  tokenVersion: number;
  sessionId?: string;
  iat?: number;
  exp?: number;
}

export interface RefreshTokenPayload {
  userId: string;
  tokenVersion: number;
  sessionId: string;
  iat?: number;
  exp?: number;
}

export class JwtUtil {
  static generateSessionId(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  static generateAccessToken(user: User, sessionId: string): string {
    const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
      userId: user.id,
      email: user.email,
      memberId: user.memberId || undefined,
      role: user.role,
      tokenVersion: user.tokenVersion,
      sessionId,
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: 'sonali-app',
      audience: 'sonali-app-users',
    });
  }

  static generateRefreshToken(user: User, sessionId: string): string {
    const payload: Omit<RefreshTokenPayload, 'iat' | 'exp'> = {
      userId: user.id,
      tokenVersion: user.tokenVersion,
      sessionId,
    };

    return jwt.sign(payload, config.jwt.refreshSecret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: 'sonali-app',
      audience: 'sonali-app-users',
    });
  }

  static async verifyAccessToken(token: string): Promise<JwtPayload> {
    try {
      // Check if token is blacklisted
      const isBlacklisted = await tokenBlacklistService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new Error('Token has been invalidated');
      }

      const payload = jwt.verify(token, config.jwt.secret, {
        issuer: 'sonali-app',
        audience: 'sonali-app-users',
      }) as JwtPayload;

      return payload;
    } catch (error) {
      logger.error('Access token verification failed:', error);
      throw new Error('Invalid access token');
    }
  }

  static async verifyRefreshToken(token: string): Promise<RefreshTokenPayload> {
    try {
      // Check if token is blacklisted
      const isBlacklisted = await tokenBlacklistService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new Error('Token has been invalidated');
      }

      const payload = jwt.verify(token, config.jwt.refreshSecret, {
        issuer: 'sonali-app',
        audience: 'sonali-app-users',
      }) as RefreshTokenPayload;

      return payload;
    } catch (error) {
      logger.error('Refresh token verification failed:', error);
      throw new Error('Invalid refresh token');
    }
  }

  static generateTokenPair(user: User, sessionId?: string): {
    accessToken: string;
    refreshToken: string;
    sessionId: string;
  } {
    const finalSessionId = sessionId || this.generateSessionId();

    return {
      accessToken: this.generateAccessToken(user, finalSessionId),
      refreshToken: this.generateRefreshToken(user, finalSessionId),
      sessionId: finalSessionId,
    };
  }

  static async invalidateToken(token: string, tokenType: 'access' | 'refresh', userId: string): Promise<void> {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        const expiresAt = new Date(decoded.exp * 1000);
        await tokenBlacklistService.blacklistToken(token, tokenType, userId, expiresAt);
      }
    } catch (error) {
      logger.error('Error invalidating token:', error);
    }
  }

  static getTokenExpiration(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any;
      return decoded && decoded.exp ? new Date(decoded.exp * 1000) : null;
    } catch (error) {
      return null;
    }
  }

  static extractTokenFromHeader(authHeader?: string): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }
}
