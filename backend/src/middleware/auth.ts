import { Request, Response, NextFunction } from 'express';
import { JwtUtil, JwtPayload } from '@/utils/jwt';
import { User, UserRole } from '@prisma/client';
import { prismaUserService } from '@/services/prisma/userService';
import { sessionService } from '@/services/sessionService';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';

// Extend Express Request interface to include user and session
declare global {
  namespace Express {
    interface Request {
      user?: User;
      userId?: string;
      sessionId?: string;
      ipAddress?: string;
    }
  }
}

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = JwtUtil.extractTokenFromHeader(req.headers.authorization);

    if (!token) {
      ResponseUtil.unauthorized(res, 'Access token is required');
      return;
    }

    const payload: JwtPayload = await JwtUtil.verifyAccessToken(token);

    const user = await prismaUserService.findUserById(payload.userId);
    if (!user) {
      ResponseUtil.unauthorized(res, 'User not found');
      return;
    }

    if (!user.isActive) {
      ResponseUtil.unauthorized(res, 'Account is deactivated');
      return;
    }

    // Check token version for invalidation
    if (payload.tokenVersion !== user.tokenVersion) {
      ResponseUtil.unauthorized(res, 'Token has been invalidated');
      return;
    }

    // Validate session if sessionId is present
    if (payload.sessionId) {
      const isSessionValid = await sessionService.isSessionValid(payload.sessionId);
      if (!isSessionValid) {
        ResponseUtil.unauthorized(res, 'Session has expired');
        return;
      }

      // Update session activity
      await sessionService.updateSessionActivity(payload.sessionId);
      req.sessionId = payload.sessionId;
    }

    // Get client IP address
    req.ipAddress = req.ip || req.connection.remoteAddress || 'unknown';

    req.user = user;
    req.userId = user.id;
    next();
  } catch (error) {
    logger.error('Authentication error:', error);
    ResponseUtil.unauthorized(res, 'Invalid or expired token');
  }
};

export const authorize = (...roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      ResponseUtil.unauthorized(res, 'Authentication required');
      return;
    }

    if (!roles.includes(req.user.role)) {
      ResponseUtil.forbidden(res, 'Insufficient permissions');
      return;
    }

    next();
  };
};

// Alias for authorize function
export const requireRole = authorize;

export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = JwtUtil.extractTokenFromHeader(req.headers.authorization);

    if (token) {
      const payload: JwtPayload = JwtUtil.verifyAccessToken(token);
      const user = await prismaUserService.findUserById(payload.userId);

      if (user && user.isActive) {
        req.user = user;
        req.userId = user.id;
      }
    }

    next();
  } catch (error) {
    // For optional auth, we don't return an error, just continue without user
    logger.debug('Optional auth failed:', error);
    next();
  }
};
