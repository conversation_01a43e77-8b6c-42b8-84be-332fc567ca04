import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';
import { config } from '@/config';

// Prisma Client instance
export const prisma = new PrismaClient({
  log: config.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  errorFormat: 'pretty',
});

// Database connection function
export const connectDatabase = async (): Promise<void> => {
  try {
    // Test the connection
    await prisma.$connect();
    logger.info('✅ Database connection established successfully.');

    // Run any pending migrations in development
    if (config.NODE_ENV === 'development') {
      logger.info('🔄 Checking for pending migrations...');
      // Note: In production, migrations should be run separately
    }
  } catch (error) {
    logger.error('❌ Unable to connect to the database:', error);
    process.exit(1);
  }
};

// Graceful shutdown
export const disconnectDatabase = async (): Promise<void> => {
  try {
    await prisma.$disconnect();
    logger.info('✅ Database connection closed successfully.');
  } catch (error) {
    logger.error('❌ Error closing database connection:', error);
  }
};

// Health check function
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
};

export default prisma;
