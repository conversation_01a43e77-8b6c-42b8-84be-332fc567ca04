import { Request, Response } from 'express';
import { installmentService, InstallmentListQuery, CollectInstallmentData } from '@/services/installmentService';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';
import { UserRole, InstallmentStatus } from '@prisma/client';

export class InstallmentController {
  async getInstallments(req: Request, res: Response): Promise<void> {
    try {
      const query: InstallmentListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        status: req.query.status as InstallmentStatus,
        branchId: req.query.branchId as string,
        memberId: req.query.memberId as string,
        loanId: req.query.loanId as string,
        sortBy: req.query.sortBy as string || 'installmentDate',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'asc',
      };

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const result = await installmentService.getInstallments(query, userRole, userBranchId);

      ResponseUtil.success(res, result, 'Installments retrieved successfully');
    } catch (error) {
      logger.error('Get installments controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve installments');
    }
  }

  async getPendingInstallments(req: Request, res: Response): Promise<void> {
    try {
      const query: InstallmentListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        branchId: req.query.branchId as string,
        memberId: req.query.memberId as string,
        loanId: req.query.loanId as string,
        sortBy: req.query.sortBy as string || 'installmentDate',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'asc',
      };

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const result = await installmentService.getPendingInstallments(query, userRole, userBranchId);

      ResponseUtil.success(res, result, 'Pending installments retrieved successfully');
    } catch (error) {
      logger.error('Get pending installments controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve pending installments');
    }
  }

  async getOverdueInstallments(req: Request, res: Response): Promise<void> {
    try {
      const query: InstallmentListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        branchId: req.query.branchId as string,
        memberId: req.query.memberId as string,
        loanId: req.query.loanId as string,
        sortBy: req.query.sortBy as string || 'installmentDate',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'asc',
      };

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const result = await installmentService.getOverdueInstallments(query, userRole, userBranchId);

      ResponseUtil.success(res, result, 'Overdue installments retrieved successfully');
    } catch (error) {
      logger.error('Get overdue installments controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve overdue installments');
    }
  }

  async collectInstallment(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const collectionData: CollectInstallmentData = {
        collectedAmount: req.body.collectedAmount,
        collectionDate: req.body.collectionDate ? new Date(req.body.collectionDate) : undefined,
        collectorId: req.userId!,
        notes: req.body.notes,
      };

      const result = await installmentService.collectInstallment(id, collectionData, userRole, userBranchId);

      ResponseUtil.success(res, result, 'Installment collected successfully');
    } catch (error) {
      logger.error('Collect installment controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
        if (error.message.includes('already been paid') || 
            error.message.includes('must be greater than zero') ||
            error.message.includes('cannot exceed')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to collect installment');
    }
  }

  async getInstallmentHistory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const history = await installmentService.getInstallmentHistory(id, userRole, userBranchId);

      ResponseUtil.success(res, history, 'Installment history retrieved successfully');
    } catch (error) {
      logger.error('Get installment history controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to retrieve installment history');
    }
  }

  async getMemberInstallments(req: Request, res: Response): Promise<void> {
    try {
      const { memberId } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const installments = await installmentService.getMemberInstallments(memberId, userRole, userBranchId);

      ResponseUtil.success(res, installments, 'Member installments retrieved successfully');
    } catch (error) {
      logger.error('Get member installments controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to retrieve member installments');
    }
  }

  async updateInstallmentStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status } = req.body;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      if (!Object.values(InstallmentStatus).includes(status)) {
        ResponseUtil.error(res, 'Invalid installment status', 400);
        return;
      }

      const installment = await installmentService.updateInstallmentStatus(id, status, userRole, userBranchId);

      ResponseUtil.success(res, installment, 'Installment status updated successfully');
    } catch (error) {
      logger.error('Update installment status controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to update installment status');
    }
  }

  async updateOverdueInstallments(req: Request, res: Response): Promise<void> {
    try {
      const count = await installmentService.updateOverdueInstallments();

      ResponseUtil.success(res, { updatedCount: count }, 'Overdue installments updated successfully');
    } catch (error) {
      logger.error('Update overdue installments controller error:', error);
      ResponseUtil.internalError(res, 'Failed to update overdue installments');
    }
  }
}

export const installmentController = new InstallmentController();
