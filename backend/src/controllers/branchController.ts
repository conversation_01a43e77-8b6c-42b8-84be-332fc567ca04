import { Request, Response } from 'express';
import { branchService, BranchListQuery, CreateBranchData, UpdateBranchData } from '@/services/branchService';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';
import { UserRole } from '@prisma/client';

export class BranchController {
  async getBranches(req: Request, res: Response): Promise<void> {
    try {
      const query: BranchListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        sortBy: req.query.sortBy as string || 'createdAt',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
      };

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const result = await branchService.getBranches(query, userRole, userBranchId);

      ResponseUtil.success(res, result, 'Branches retrieved successfully');
    } catch (error) {
      logger.error('Get branches controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve branches');
    }
  }

  async createBranch(req: Request, res: Response): Promise<void> {
    try {
      const branchData: CreateBranchData = {
        name: req.body.name,
        address: req.body.address,
        managerId: req.body.managerId,
      };

      const branch = await branchService.createBranch(branchData);

      ResponseUtil.success(res, branch, 'Branch created successfully', 201);
    } catch (error) {
      logger.error('Create branch controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found') || 
            error.message.includes('not a manager') ||
            error.message.includes('not active')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to create branch');
    }
  }

  async getBranchById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const branch = await branchService.getBranchById(id, userRole, userBranchId);

      if (!branch) {
        ResponseUtil.notFound(res, 'Branch not found');
        return;
      }

      ResponseUtil.success(res, branch, 'Branch retrieved successfully');
    } catch (error) {
      logger.error('Get branch by ID controller error:', error);
      
      if (error instanceof Error && error.message.includes('Access denied')) {
        ResponseUtil.forbidden(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve branch');
    }
  }

  async updateBranch(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData: UpdateBranchData = {
        name: req.body.name,
        address: req.body.address,
        managerId: req.body.managerId,
        isActive: req.body.isActive,
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof UpdateBranchData] === undefined) {
          delete updateData[key as keyof UpdateBranchData];
        }
      });

      const branch = await branchService.updateBranch(id, updateData);

      ResponseUtil.success(res, branch, 'Branch updated successfully');
    } catch (error) {
      logger.error('Update branch controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('not a manager') || 
            error.message.includes('not active')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to update branch');
    }
  }

  async deleteBranch(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      await branchService.deleteBranch(id);

      ResponseUtil.success(res, null, 'Branch deleted successfully');
    } catch (error) {
      logger.error('Delete branch controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Cannot delete')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to delete branch');
    }
  }

  async getBranchUsers(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const users = await branchService.getBranchUsers(id, userRole, userBranchId);

      ResponseUtil.success(res, users, 'Branch users retrieved successfully');
    } catch (error) {
      logger.error('Get branch users controller error:', error);
      
      if (error instanceof Error && error.message.includes('Access denied')) {
        ResponseUtil.forbidden(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve branch users');
    }
  }

  async getBranchMembers(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const members = await branchService.getBranchMembers(id, userRole, userBranchId);

      ResponseUtil.success(res, members, 'Branch members retrieved successfully');
    } catch (error) {
      logger.error('Get branch members controller error:', error);
      
      if (error instanceof Error && error.message.includes('Access denied')) {
        ResponseUtil.forbidden(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve branch members');
    }
  }

  async getBranchPerformance(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const performance = await branchService.getBranchPerformance(id, userRole, userBranchId);

      ResponseUtil.success(res, performance, 'Branch performance retrieved successfully');
    } catch (error) {
      logger.error('Get branch performance controller error:', error);
      
      if (error instanceof Error && error.message.includes('Access denied')) {
        ResponseUtil.forbidden(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve branch performance');
    }
  }
}

export const branchController = new BranchController();
