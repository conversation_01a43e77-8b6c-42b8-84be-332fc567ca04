import { Request, Response } from 'express';
import { memberService, MemberListQuery, CreateMemberData, UpdateMemberData, MemberSearchQuery } from '@/services/memberService';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';
import { UserRole } from '@prisma/client';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// Configure multer for photo uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'members');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `member-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

export class MemberController {
  async getMembers(req: Request, res: Response): Promise<void> {
    try {
      const query: MemberListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        branchId: req.query.branchId as string,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        sortBy: req.query.sortBy as string || 'createdAt',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
      };

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const result = await memberService.getMembers(query, userRole, userBranchId);

      ResponseUtil.success(res, result, 'Members retrieved successfully');
    } catch (error) {
      logger.error('Get members controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve members');
    }
  }

  async createMember(req: Request, res: Response): Promise<void> {
    try {
      const memberData: CreateMemberData = {
        memberId: req.body.memberId,
        name: req.body.name,
        fatherOrHusbandName: req.body.fatherOrHusbandName,
        motherName: req.body.motherName,
        presentAddress: req.body.presentAddress,
        permanentAddress: req.body.permanentAddress,
        nidNumber: req.body.nidNumber,
        dateOfBirth: new Date(req.body.dateOfBirth),
        religion: req.body.religion,
        phoneNumber: req.body.phoneNumber,
        bloodGroup: req.body.bloodGroup,
        occupation: req.body.occupation,
        referenceId: req.body.referenceId,
        branchId: req.body.branchId || req.user?.branchId,
        createdBy: req.userId!,
      };

      const member = await memberService.createMember(memberData);

      ResponseUtil.success(res, member, 'Member created successfully', 201);
    } catch (error) {
      logger.error('Create member controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('already exists')) {
          ResponseUtil.conflict(res, error.message);
          return;
        }
        if (error.message.includes('not found') || 
            error.message.includes('not active')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to create member');
    }
  }

  async getMemberById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const member = await memberService.getMemberById(id, userRole, userBranchId);

      if (!member) {
        ResponseUtil.notFound(res, 'Member not found');
        return;
      }

      ResponseUtil.success(res, member, 'Member retrieved successfully');
    } catch (error) {
      logger.error('Get member by ID controller error:', error);
      
      if (error instanceof Error && error.message.includes('Access denied')) {
        ResponseUtil.forbidden(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve member');
    }
  }

  async updateMember(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const updateData: UpdateMemberData = {
        name: req.body.name,
        fatherOrHusbandName: req.body.fatherOrHusbandName,
        motherName: req.body.motherName,
        presentAddress: req.body.presentAddress,
        permanentAddress: req.body.permanentAddress,
        nidNumber: req.body.nidNumber,
        dateOfBirth: req.body.dateOfBirth ? new Date(req.body.dateOfBirth) : undefined,
        religion: req.body.religion,
        phoneNumber: req.body.phoneNumber,
        bloodGroup: req.body.bloodGroup,
        occupation: req.body.occupation,
        referenceId: req.body.referenceId,
        isActive: req.body.isActive,
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof UpdateMemberData] === undefined) {
          delete updateData[key as keyof UpdateMemberData];
        }
      });

      const member = await memberService.updateMember(id, updateData, userRole, userBranchId);

      ResponseUtil.success(res, member, 'Member updated successfully');
    } catch (error) {
      logger.error('Update member controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
        if (error.message.includes('already exists')) {
          ResponseUtil.conflict(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to update member');
    }
  }

  async deleteMember(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      await memberService.deleteMember(id, userRole, userBranchId);

      ResponseUtil.success(res, null, 'Member deleted successfully');
    } catch (error) {
      logger.error('Delete member controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
        if (error.message.includes('Cannot delete')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to delete member');
    }
  }

  async searchMembers(req: Request, res: Response): Promise<void> {
    try {
      const searchQuery: MemberSearchQuery = {
        query: req.query.q as string,
        branchId: req.query.branchId as string,
        limit: parseInt(req.query.limit as string) || 10,
      };

      if (!searchQuery.query) {
        ResponseUtil.error(res, 'Search query is required', 400);
        return;
      }

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const members = await memberService.searchMembers(searchQuery, userRole, userBranchId);

      ResponseUtil.success(res, members, 'Members search completed');
    } catch (error) {
      logger.error('Search members controller error:', error);
      ResponseUtil.internalError(res, 'Failed to search members');
    }
  }

  async uploadMemberPhoto(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      if (!req.file) {
        ResponseUtil.error(res, 'Photo file is required', 400);
        return;
      }

      const photoPath = path.relative(process.cwd(), req.file.path);
      const member = await memberService.uploadMemberPhoto(id, photoPath, userRole, userBranchId);

      ResponseUtil.success(res, { member, photoUrl: `/uploads/${photoPath}` }, 'Photo uploaded successfully');
    } catch (error) {
      logger.error('Upload member photo controller error:', error);
      
      // Clean up uploaded file if there was an error
      if (req.file) {
        fs.unlink(req.file.path, (err) => {
          if (err) logger.error('Failed to delete uploaded file:', err);
        });
      }
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to upload photo');
    }
  }

  async exportMembers(req: Request, res: Response): Promise<void> {
    try {
      const query: MemberListQuery = {
        search: req.query.search as string,
        branchId: req.query.branchId as string,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
      };

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const buffer = await memberService.exportMembers(query, userRole, userBranchId);

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=members-export-${new Date().toISOString().split('T')[0]}.xlsx`);
      
      res.send(buffer);
    } catch (error) {
      logger.error('Export members controller error:', error);
      ResponseUtil.internalError(res, 'Failed to export members');
    }
  }

  // Middleware for photo upload
  static uploadPhoto = upload.single('photo');
}

export const memberController = new MemberController();
