import { Request, Response } from 'express';
import { 
  loanService, 
  LoanApplicationListQuery, 
  CreateLoanApplicationData, 
  UpdateLoanApplicationData,
  LoanListQuery,
  LoanCalculationData 
} from '@/services/loanService';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';
import { UserRole } from '@prisma/client';

export class LoanController {
  async getLoanApplications(req: Request, res: Response): Promise<void> {
    try {
      const query: LoanApplicationListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        status: req.query.status as any,
        branchId: req.query.branchId as string,
        memberId: req.query.memberId as string,
        sortBy: req.query.sortBy as string || 'appliedAt',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
      };

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const result = await loanService.getLoanApplications(query, userRole, userBranchId);

      ResponseUtil.success(res, result, 'Loan applications retrieved successfully');
    } catch (error) {
      logger.error('Get loan applications controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve loan applications');
    }
  }

  async createLoanApplication(req: Request, res: Response): Promise<void> {
    try {
      const applicationData: CreateLoanApplicationData = {
        memberId: req.body.memberId,
        appliedAmount: req.body.appliedAmount,
        reason: req.body.reason,
        loanCycleNumber: req.body.loanCycleNumber || 1,
        recommender: req.body.recommender,
        advancePayment: req.body.advancePayment || 0,
      };

      const application = await loanService.createLoanApplication(applicationData);

      ResponseUtil.success(res, application, 'Loan application created successfully', 201);
    } catch (error) {
      logger.error('Create loan application controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found') || 
            error.message.includes('not active') ||
            error.message.includes('pending loan application')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to create loan application');
    }
  }

  async getLoanApplicationById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const application = await loanService.getLoanApplicationById(id, userRole, userBranchId);

      if (!application) {
        ResponseUtil.notFound(res, 'Loan application not found');
        return;
      }

      ResponseUtil.success(res, application, 'Loan application retrieved successfully');
    } catch (error) {
      logger.error('Get loan application by ID controller error:', error);
      
      if (error instanceof Error && error.message.includes('Access denied')) {
        ResponseUtil.forbidden(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve loan application');
    }
  }

  async updateLoanApplication(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const updateData: UpdateLoanApplicationData = {
        appliedAmount: req.body.appliedAmount,
        reason: req.body.reason,
        recommender: req.body.recommender,
        advancePayment: req.body.advancePayment,
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof UpdateLoanApplicationData] === undefined) {
          delete updateData[key as keyof UpdateLoanApplicationData];
        }
      });

      const application = await loanService.updateLoanApplication(id, updateData, userRole, userBranchId);

      ResponseUtil.success(res, application, 'Loan application updated successfully');
    } catch (error) {
      logger.error('Update loan application controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
        if (error.message.includes('Only pending')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to update loan application');
    }
  }

  async approveLoanApplication(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const reviewerId = req.userId!;

      const result = await loanService.approveLoanApplication(id, reviewerId);

      ResponseUtil.success(res, result, 'Loan application approved successfully');
    } catch (error) {
      logger.error('Approve loan application controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Only pending')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to approve loan application');
    }
  }

  async rejectLoanApplication(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const reviewerId = req.userId!;
      const { rejectionReason } = req.body;

      const application = await loanService.rejectLoanApplication(id, reviewerId, rejectionReason);

      ResponseUtil.success(res, application, 'Loan application rejected successfully');
    } catch (error) {
      logger.error('Reject loan application controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Only pending')) {
          ResponseUtil.error(res, error.message, 400);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to reject loan application');
    }
  }

  async getLoans(req: Request, res: Response): Promise<void> {
    try {
      const query: LoanListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        branchId: req.query.branchId as string,
        memberId: req.query.memberId as string,
        sortBy: req.query.sortBy as string || 'loanDate',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
      };

      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const result = await loanService.getLoans(query, userRole, userBranchId);

      ResponseUtil.success(res, result, 'Loans retrieved successfully');
    } catch (error) {
      logger.error('Get loans controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve loans');
    }
  }

  async getLoanById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const loan = await loanService.getLoanById(id, userRole, userBranchId);

      if (!loan) {
        ResponseUtil.notFound(res, 'Loan not found');
        return;
      }

      ResponseUtil.success(res, loan, 'Loan retrieved successfully');
    } catch (error) {
      logger.error('Get loan by ID controller error:', error);
      
      if (error instanceof Error && error.message.includes('Access denied')) {
        ResponseUtil.forbidden(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to retrieve loan');
    }
  }

  async getLoanInstallments(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role as UserRole;
      const userBranchId = req.user?.branchId;

      const installments = await loanService.getLoanInstallments(id, userRole, userBranchId);

      ResponseUtil.success(res, installments, 'Loan installments retrieved successfully');
    } catch (error) {
      logger.error('Get loan installments controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Access denied')) {
          ResponseUtil.forbidden(res, error.message);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to retrieve loan installments');
    }
  }

  async calculateLoan(req: Request, res: Response): Promise<void> {
    try {
      const calculationData: LoanCalculationData = {
        loanAmount: req.body.loanAmount,
        repaymentDuration: req.body.repaymentDuration,
        repaymentMethod: req.body.repaymentMethod,
        advancePayment: req.body.advancePayment || 0,
        interestRate: req.body.interestRate || 10,
      };

      const result = loanService.calculateLoan(calculationData);

      ResponseUtil.success(res, result, 'Loan calculation completed successfully');
    } catch (error) {
      logger.error('Calculate loan controller error:', error);
      ResponseUtil.internalError(res, 'Failed to calculate loan');
    }
  }
}

export const loanController = new LoanController();
