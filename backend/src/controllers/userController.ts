import { Request, Response } from 'express';
import { userManagementService, UserListQuery } from '@/services/userManagementService';
import { CreateUserData } from '@/services/prisma/userService';
import { ResponseUtil } from '@/utils/response';
import { logger } from '@/utils/logger';
import { UserRole } from '@prisma/client';
import multer from 'multer';

export class UserController {
  async getUsers(req: Request, res: Response): Promise<void> {
    try {
      const query: UserListQuery = {
        page: parseInt(req.query.page as string) || 1,
        limit: parseInt(req.query.limit as string) || 10,
        search: req.query.search as string,
        role: req.query.role as UserRole,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        branchId: req.query.branchId as string,
        sortBy: req.query.sortBy as string || 'createdAt',
        sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
      };

      const result = await userManagementService.getUsers(query);

      ResponseUtil.success(res, result, 'Users retrieved successfully');
    } catch (error) {
      logger.error('Get users controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve users');
    }
  }

  async createUser(req: Request, res: Response): Promise<void> {
    try {
      const userData: CreateUserData = {
        name: req.body.name,
        email: req.body.email,
        password: req.body.password,
        role: req.body.role || UserRole.member,
        memberId: req.body.memberId,
        branchId: req.body.branchId,
      };

      const user = await userManagementService.createUser(userData);

      ResponseUtil.success(res, user, 'User created successfully', 201);
    } catch (error) {
      logger.error('Create user controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('already exists') || 
            error.message.includes('already taken')) {
          ResponseUtil.error(res, error.message, 409);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to create user');
    }
  }

  async getUserById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const user = await userManagementService.getUserById(id);

      if (!user) {
        ResponseUtil.notFound(res, 'User not found');
        return;
      }

      ResponseUtil.success(res, user, 'User retrieved successfully');
    } catch (error) {
      logger.error('Get user by ID controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve user');
    }
  }

  async updateUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const updateData: Partial<CreateUserData> = {
        name: req.body.name,
        email: req.body.email,
        role: req.body.role,
        memberId: req.body.memberId,
        branchId: req.body.branchId,
        isActive: req.body.isActive,
      };

      // Remove undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof CreateUserData] === undefined) {
          delete updateData[key as keyof CreateUserData];
        }
      });

      const user = await userManagementService.updateUser(id, updateData);

      ResponseUtil.success(res, user, 'User updated successfully');
    } catch (error) {
      logger.error('Update user controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('already taken')) {
          ResponseUtil.error(res, error.message, 409);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to update user');
    }
  }

  async deleteUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      // Prevent users from deleting themselves
      if (req.userId === id) {
        ResponseUtil.error(res, 'Cannot delete your own account', 400);
        return;
      }

      await userManagementService.deleteUser(id);

      ResponseUtil.success(res, null, 'User deleted successfully');
    } catch (error) {
      logger.error('Delete user controller error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          ResponseUtil.notFound(res, error.message);
          return;
        }
        if (error.message.includes('Cannot delete')) {
          ResponseUtil.error(res, error.message, 403);
          return;
        }
      }

      ResponseUtil.internalError(res, 'Failed to delete user');
    }
  }

  async resetUserPassword(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { newPassword } = req.body;

      if (!newPassword) {
        ResponseUtil.error(res, 'New password is required', 400);
        return;
      }

      await userManagementService.resetUserPassword(id, newPassword);

      ResponseUtil.success(res, null, 'User password reset successfully');
    } catch (error) {
      logger.error('Reset user password controller error:', error);
      
      if (error instanceof Error && error.message.includes('not found')) {
        ResponseUtil.notFound(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to reset user password');
    }
  }

  async toggleUserStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      
      // Prevent users from deactivating themselves
      if (req.userId === id) {
        ResponseUtil.error(res, 'Cannot change your own account status', 400);
        return;
      }

      const user = await userManagementService.toggleUserStatus(id);

      ResponseUtil.success(res, user, 'User status updated successfully');
    } catch (error) {
      logger.error('Toggle user status controller error:', error);
      
      if (error instanceof Error && error.message.includes('not found')) {
        ResponseUtil.notFound(res, error.message);
        return;
      }

      ResponseUtil.internalError(res, 'Failed to update user status');
    }
  }

  async bulkImportUsers(req: Request, res: Response): Promise<void> {
    try {
      const { users } = req.body;

      if (!Array.isArray(users) || users.length === 0) {
        ResponseUtil.error(res, 'Users array is required and cannot be empty', 400);
        return;
      }

      const result = await userManagementService.bulkImportUsers(users);

      ResponseUtil.success(res, result, 'Bulk import completed');
    } catch (error) {
      logger.error('Bulk import users controller error:', error);
      ResponseUtil.internalError(res, 'Failed to import users');
    }
  }

  async exportUsers(req: Request, res: Response): Promise<void> {
    try {
      const query: UserListQuery = {
        search: req.query.search as string,
        role: req.query.role as UserRole,
        isActive: req.query.isActive ? req.query.isActive === 'true' : undefined,
        branchId: req.query.branchId as string,
      };

      const buffer = await userManagementService.exportUsers(query);

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=users-export-${new Date().toISOString().split('T')[0]}.xlsx`);
      
      res.send(buffer);
    } catch (error) {
      logger.error('Export users controller error:', error);
      ResponseUtil.internalError(res, 'Failed to export users');
    }
  }

  async getUserStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await userManagementService.getUserStats();

      ResponseUtil.success(res, stats, 'User statistics retrieved successfully');
    } catch (error) {
      logger.error('Get user stats controller error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve user statistics');
    }
  }
}

export const userController = new UserController();
