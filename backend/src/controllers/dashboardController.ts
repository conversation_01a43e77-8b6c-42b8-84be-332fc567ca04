import { Request, Response } from 'express';
import { dashboardService } from '../services/dashboardService';
import { ResponseUtil } from '../utils/response';
import { logger } from '../utils/logger';
import { UserRole } from '@prisma/client';

export class DashboardController {
  
  /**
   * Get Admin Dashboard Data
   */
  async getAdminDashboard(req: Request, res: Response): Promise<void> {
    try {
      const data = await dashboardService.getAdminDashboardData();
      ResponseUtil.success(res, data, 'Admin dashboard data retrieved successfully');
    } catch (error) {
      logger.error('Get admin dashboard error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve admin dashboard data');
    }
  }

  /**
   * Get Manager Dashboard Data
   */
  async getManagerDashboard(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const branchId = req.user?.branchId;
      
      if (!userId || !branchId) {
        ResponseUtil.badRequest(res, 'User or branch information missing');
        return;
      }

      const data = await dashboardService.getManagerDashboardData(userId, branchId);
      ResponseUtil.success(res, data, 'Manager dashboard data retrieved successfully');
    } catch (error) {
      logger.error('Get manager dashboard error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve manager dashboard data');
    }
  }

  /**
   * Get Field Officer Dashboard Data
   */
  async getFieldOfficerDashboard(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const branchId = req.user?.branchId;
      
      if (!userId || !branchId) {
        ResponseUtil.badRequest(res, 'User or branch information missing');
        return;
      }

      const data = await dashboardService.getFieldOfficerDashboardData(userId, branchId);
      ResponseUtil.success(res, data, 'Field officer dashboard data retrieved successfully');
    } catch (error) {
      logger.error('Get field officer dashboard error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve field officer dashboard data');
    }
  }

  /**
   * Get Member Dashboard Data
   */
  async getMemberDashboard(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const memberId = req.user?.memberId;
      
      if (!userId || !memberId) {
        ResponseUtil.badRequest(res, 'User or member information missing');
        return;
      }

      const data = await dashboardService.getMemberDashboardData(userId, memberId);
      ResponseUtil.success(res, data, 'Member dashboard data retrieved successfully');
    } catch (error) {
      logger.error('Get member dashboard error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve member dashboard data');
    }
  }

  /**
   * Get Recent Activities
   */
  async getRecentActivities(req: Request, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 10;
      const userId = req.user?.id;
      const userRole = req.user?.role;
      const branchId = req.user?.branchId;

      if (!userId || !userRole) {
        ResponseUtil.badRequest(res, 'User information missing');
        return;
      }

      const activities = await dashboardService.getRecentActivities(
        userId, 
        userRole as UserRole, 
        branchId, 
        limit
      );
      
      ResponseUtil.success(res, activities, 'Recent activities retrieved successfully');
    } catch (error) {
      logger.error('Get recent activities error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve recent activities');
    }
  }

  /**
   * Get Upcoming Collections for Field Officers
   */
  async getUpcomingCollections(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      const userRole = req.user?.role;
      const branchId = req.user?.branchId;

      if (!userId || !userRole) {
        ResponseUtil.badRequest(res, 'User information missing');
        return;
      }

      if (userRole !== UserRole.field_officer && userRole !== UserRole.manager && userRole !== UserRole.admin) {
        ResponseUtil.forbidden(res, 'Access denied for this role');
        return;
      }

      const collections = await dashboardService.getUpcomingCollections(userId, userRole as UserRole, branchId);
      ResponseUtil.success(res, collections, 'Upcoming collections retrieved successfully');
    } catch (error) {
      logger.error('Get upcoming collections error:', error);
      ResponseUtil.internalError(res, 'Failed to retrieve upcoming collections');
    }
  }
}

export const dashboardController = new DashboardController();
