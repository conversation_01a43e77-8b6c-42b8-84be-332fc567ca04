import express, { Application, Request, Response, NextFunction } from 'express';
import path from 'path';
import { config } from './config';
import { logger } from './utils/logger';
import { ResponseUtil } from './utils/response';
import authRoutes from './routes/authRoutes';
import userRoutes from './routes/userRoutes';
import branchRoutes from './routes/branchRoutes';
import memberRoutes from './routes/memberRoutes';
import loanRoutes from './routes/loanRoutes';
import installmentRoutes from './routes/installmentRoutes';
import financialRoutes from './routes/financialRoutes';
import {
  corsOptions,
  securityHeaders,
  generalRateLimit,
  sanitizeInput,
  preventSQLInjection,
  preventXSS,
  requestLogger,
  apiSecurityHeaders,
  validateRequestSize,
} from './middleware/security';
import cors from 'cors';
import morgan from 'morgan';

export const createApp = (): Application => {
  const app = express();

  // Trust proxy for accurate IP addresses
  app.set('trust proxy', 1);

  // Request size validation
  app.use(validateRequestSize(10 * 1024 * 1024)); // 10MB limit

  // Security headers
  app.use(securityHeaders);
  app.use(apiSecurityHeaders);

  // CORS configuration
  app.use(cors(corsOptions));

  // Request logging
  app.use(requestLogger);

  // Rate limiting
  app.use('/api/', generalRateLimit);

  // Logging middleware
  if (config.NODE_ENV === 'development') {
    app.use(morgan('dev'));
  } else {
    app.use(morgan('combined'));
  }

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Security middleware for input validation
  app.use(sanitizeInput);
  app.use(preventSQLInjection);
  app.use(preventXSS);

  // Health check endpoint
  app.get('/health', (req: Request, res: Response) => {
    ResponseUtil.success(res, {
      status: 'OK',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.NODE_ENV,
      version: config.app.version,
    }, 'Server is healthy');
  });

  // API routes
  app.use('/api', (req: Request, res: Response, next: NextFunction) => {
    logger.info(`${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });
    next();
  });

  // Serve static files for uploads
  app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));

  // Add route handlers
  app.use('/api/auth', authRoutes);
  app.use('/api/users', userRoutes);
  app.use('/api/branches', branchRoutes);
  app.use('/api/members', memberRoutes);
  app.use('/api', loanRoutes);
  app.use('/api/installments', installmentRoutes);
  app.use('/api', financialRoutes);

  // API documentation endpoint
  app.get('/api', (req: Request, res: Response) => {
    ResponseUtil.success(res, {
      name: config.app.name,
      version: config.app.version,
      description: config.app.description,
      endpoints: {
        health: '/health',
        auth: '/api/auth',
        users: '/api/users',
      },
    }, 'API Information');
  });

  // 404 handler for API routes
  app.use('/api/*', (req: Request, res: Response) => {
    ResponseUtil.notFound(res, `API endpoint ${req.originalUrl} not found`);
  });

  // Global error handler
  app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
    logger.error('Unhandled error:', {
      error: err.message,
      stack: err.stack,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
    });

    if (config.NODE_ENV === 'development') {
      ResponseUtil.internalError(res, err.message);
    } else {
      ResponseUtil.internalError(res, 'Something went wrong!');
    }
  });

  // Catch-all handler for non-API routes
  app.use('*', (req: Request, res: Response) => {
    ResponseUtil.notFound(res, `Route ${req.originalUrl} not found`);
  });

  return app;
};
