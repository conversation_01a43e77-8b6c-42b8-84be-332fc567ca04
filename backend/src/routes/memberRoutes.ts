import { Router } from 'express';
import { member<PERSON><PERSON>roller, MemberController } from '@/controllers/memberController';
import { authenticate, requireRole } from '@/middleware/auth';
import { body, param, query } from 'express-validator';
import { handleValidationErrors } from '@/middleware/validation';
import { UserRole } from '@prisma/client';

const router = Router();

// Validation middleware
const validateMemberId = [
  param('id')
    .isUUID()
    .withMessage('Invalid member ID format'),
];

const validateCreateMember = [
  body('memberId')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Member ID must be between 3 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Member ID must contain only uppercase letters and numbers'),
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('fatherOr<PERSON>usbandName')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Father/Husband name must be between 2 and 100 characters'),
  body('motherName')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Mother name must be between 2 and 100 characters'),
  body('presentAddress')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Present address must be between 10 and 500 characters'),
  body('permanentAddress')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Permanent address must be between 10 and 500 characters'),
  body('nidNumber')
    .trim()
    .isLength({ min: 10, max: 20 })
    .withMessage('NID number must be between 10 and 20 characters')
    .matches(/^[0-9]+$/)
    .withMessage('NID number must contain only numbers'),
  body('dateOfBirth')
    .isISO8601()
    .withMessage('Valid date of birth is required'),
  body('religion')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Religion must be between 2 and 50 characters'),
  body('phoneNumber')
    .trim()
    .matches(/^(\+88)?01[3-9]\d{8}$/)
    .withMessage('Valid Bangladeshi phone number is required'),
  body('bloodGroup')
    .optional()
    .isIn(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'])
    .withMessage('Invalid blood group'),
  body('occupation')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Occupation must be between 2 and 100 characters'),
  body('referenceId')
    .optional()
    .isUUID()
    .withMessage('Invalid reference member ID format'),
  body('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
];

const validateUpdateMember = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('fatherOrHusbandName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Father/Husband name must be between 2 and 100 characters'),
  body('motherName')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Mother name must be between 2 and 100 characters'),
  body('presentAddress')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Present address must be between 10 and 500 characters'),
  body('permanentAddress')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Permanent address must be between 10 and 500 characters'),
  body('nidNumber')
    .optional()
    .trim()
    .isLength({ min: 10, max: 20 })
    .withMessage('NID number must be between 10 and 20 characters')
    .matches(/^[0-9]+$/)
    .withMessage('NID number must contain only numbers'),
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Valid date of birth is required'),
  body('religion')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Religion must be between 2 and 50 characters'),
  body('phoneNumber')
    .optional()
    .trim()
    .matches(/^(\+88)?01[3-9]\d{8}$/)
    .withMessage('Valid Bangladeshi phone number is required'),
  body('bloodGroup')
    .optional()
    .isIn(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'])
    .withMessage('Invalid blood group'),
  body('occupation')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Occupation must be between 2 and 100 characters'),
  body('referenceId')
    .optional()
    .isUUID()
    .withMessage('Invalid reference member ID format'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
];

const validateQueryParams = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
  query('sortBy')
    .optional()
    .isIn(['name', 'memberId', 'createdAt', 'phoneNumber'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
];

const validateSearchQuery = [
  query('q')
    .notEmpty()
    .withMessage('Search query is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Search query must be between 2 and 100 characters'),
  query('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50'),
];

// All routes require authentication
router.use(authenticate);

// GET /api/members - Get members (with role-based filtering)
router.get(
  '/',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateQueryParams,
  handleValidationErrors,
  memberController.getMembers.bind(memberController)
);

// GET /api/members/search - Search members with autocomplete
router.get(
  '/search',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateSearchQuery,
  handleValidationErrors,
  memberController.searchMembers.bind(memberController)
);

// GET /api/members/export - Export members data (admin only)
router.get(
  '/export',
  requireRole([UserRole.admin]),
  validateQueryParams,
  handleValidationErrors,
  memberController.exportMembers.bind(memberController)
);

// POST /api/members - Create new member
router.post(
  '/',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateCreateMember,
  handleValidationErrors,
  memberController.createMember.bind(memberController)
);

// GET /api/members/:id - Get member details
router.get(
  '/:id',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateMemberId,
  handleValidationErrors,
  memberController.getMemberById.bind(memberController)
);

// PUT /api/members/:id - Update member
router.put(
  '/:id',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateMemberId,
  validateUpdateMember,
  handleValidationErrors,
  memberController.updateMember.bind(memberController)
);

// DELETE /api/members/:id - Delete member
router.delete(
  '/:id',
  requireRole([UserRole.admin, UserRole.manager]),
  validateMemberId,
  handleValidationErrors,
  memberController.deleteMember.bind(memberController)
);

// POST /api/members/:id/upload-photo - Upload member photo
router.post(
  '/:id/upload-photo',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateMemberId,
  handleValidationErrors,
  MemberController.uploadPhoto,
  memberController.uploadMemberPhoto.bind(memberController)
);

export default router;
