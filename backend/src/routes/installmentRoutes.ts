import { Router } from 'express';
import { installmentController } from '@/controllers/installmentController';
import { authenticate, requireRole } from '@/middleware/auth';
import { body, param, query } from 'express-validator';
import { handleValidationErrors } from '@/middleware/validation';
import { UserRole, InstallmentStatus } from '@prisma/client';

const router = Router();

// Validation middleware
const validateInstallmentId = [
  param('id')
    .isUUID()
    .withMessage('Invalid installment ID format'),
];

const validateMemberId = [
  param('memberId')
    .isUUID()
    .withMessage('Invalid member ID format'),
];

const validateCollectInstallment = [
  body('collectedAmount')
    .isFloat({ min: 0.01 })
    .withMessage('Collected amount must be greater than zero'),
  body('collectionDate')
    .optional()
    .isISO8601()
    .withMessage('Valid collection date is required'),
  body('notes')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notes must not exceed 500 characters'),
];

const validateUpdateStatus = [
  body('status')
    .isIn(Object.values(InstallmentStatus))
    .withMessage('Invalid installment status'),
];

const validateQueryParams = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('status')
    .optional()
    .isIn(Object.values(InstallmentStatus))
    .withMessage('Invalid status'),
  query('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
  query('memberId')
    .optional()
    .isUUID()
    .withMessage('Invalid member ID format'),
  query('loanId')
    .optional()
    .isUUID()
    .withMessage('Invalid loan ID format'),
  query('sortBy')
    .optional()
    .isIn(['installmentDate', 'installmentAmount', 'status', 'installmentNo'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
];

// All routes require authentication
router.use(authenticate);

// GET /api/installments - Get installments (role-based filtering)
router.get(
  '/',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateQueryParams,
  handleValidationErrors,
  installmentController.getInstallments.bind(installmentController)
);

// GET /api/installments/pending - Get pending installments
router.get(
  '/pending',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateQueryParams,
  handleValidationErrors,
  installmentController.getPendingInstallments.bind(installmentController)
);

// GET /api/installments/overdue - Get overdue installments
router.get(
  '/overdue',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateQueryParams,
  handleValidationErrors,
  installmentController.getOverdueInstallments.bind(installmentController)
);

// POST /api/installments/update-overdue - Update overdue installments (admin/manager only)
router.post(
  '/update-overdue',
  requireRole([UserRole.admin, UserRole.manager]),
  installmentController.updateOverdueInstallments.bind(installmentController)
);

// GET /api/installments/member/:memberId - Get member installments
router.get(
  '/member/:memberId',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateMemberId,
  handleValidationErrors,
  installmentController.getMemberInstallments.bind(installmentController)
);

// POST /api/installments/:id/collect - Collect installment payment
router.post(
  '/:id/collect',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateInstallmentId,
  validateCollectInstallment,
  handleValidationErrors,
  installmentController.collectInstallment.bind(installmentController)
);

// GET /api/installments/:id/history - Get installment payment history
router.get(
  '/:id/history',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateInstallmentId,
  handleValidationErrors,
  installmentController.getInstallmentHistory.bind(installmentController)
);

// PUT /api/installments/:id/status - Update installment status
router.put(
  '/:id/status',
  requireRole([UserRole.admin, UserRole.manager]),
  validateInstallmentId,
  validateUpdateStatus,
  handleValidationErrors,
  installmentController.updateInstallmentStatus.bind(installmentController)
);

export default router;
