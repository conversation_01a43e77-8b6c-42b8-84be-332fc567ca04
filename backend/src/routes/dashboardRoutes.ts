import { Router } from 'express';
import { dashboardController } from '../controllers/dashboardController';
import { authenticate, requireRole } from '../middleware/auth';
import { UserRole } from '@prisma/client';
import { query } from 'express-validator';
import { handleValidationErrors } from '../middleware/validation';

const router = Router();

// Validation middleware
const validateActivitiesQuery = [
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
];

// All dashboard routes require authentication
router.use(authenticate);

// GET /api/dashboard/admin - Get admin dashboard data
router.get(
  '/admin',
  requireRole([UserRole.admin]),
  dashboardController.getAdminDashboard.bind(dashboardController)
);

// GET /api/dashboard/manager - Get manager dashboard data
router.get(
  '/manager',
  requireRole([UserRole.manager]),
  dashboardController.getManagerDashboard.bind(dashboardController)
);

// GET /api/dashboard/field-officer - Get field officer dashboard data
router.get(
  '/field-officer',
  requireRole([UserRole.field_officer]),
  dashboardController.getFieldOfficerDashboard.bind(dashboardController)
);

// GET /api/dashboard/member - Get member dashboard data
router.get(
  '/member',
  requireRole([UserRole.member]),
  dashboardController.getMemberDashboard.bind(dashboardController)
);

// GET /api/dashboard/activities - Get recent activities
router.get(
  '/activities',
  validateActivitiesQuery,
  handleValidationErrors,
  dashboardController.getRecentActivities.bind(dashboardController)
);

// GET /api/dashboard/upcoming-collections - Get upcoming collections
router.get(
  '/upcoming-collections',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  dashboardController.getUpcomingCollections.bind(dashboardController)
);

export default router;
