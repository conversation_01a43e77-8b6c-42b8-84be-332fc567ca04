import { Router } from 'express';
import { loanController } from '@/controllers/loanController';
import { authenticate, requireRole } from '@/middleware/auth';
import { body, param, query } from 'express-validator';
import { handleValidationErrors } from '@/middleware/validation';
import { UserRole, LoanApplicationStatus, RepaymentMethod } from '@prisma/client';

const router = Router();

// Validation middleware
const validateLoanApplicationId = [
  param('id')
    .isUUID()
    .withMessage('Invalid loan application ID format'),
];

const validateLoanId = [
  param('id')
    .isUUID()
    .withMessage('Invalid loan ID format'),
];

const validateCreateLoanApplication = [
  body('memberId')
    .isUUID()
    .withMessage('Valid member ID is required'),
  body('appliedAmount')
    .isFloat({ min: 1000, max: 1000000 })
    .withMessage('Applied amount must be between 1,000 and 1,000,000'),
  body('reason')
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Reason must be between 10 and 500 characters'),
  body('loanCycleNumber')
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage('Loan cycle number must be between 1 and 10'),
  body('recommender')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Recommender name must be between 2 and 100 characters'),
  body('advancePayment')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Advance payment must be a positive number'),
];

const validateUpdateLoanApplication = [
  body('appliedAmount')
    .optional()
    .isFloat({ min: 1000, max: 1000000 })
    .withMessage('Applied amount must be between 1,000 and 1,000,000'),
  body('reason')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Reason must be between 10 and 500 characters'),
  body('recommender')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Recommender name must be between 2 and 100 characters'),
  body('advancePayment')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Advance payment must be a positive number'),
];

const validateRejectApplication = [
  body('rejectionReason')
    .optional()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Rejection reason must be between 10 and 500 characters'),
];

const validateLoanCalculation = [
  body('loanAmount')
    .isFloat({ min: 1000, max: 1000000 })
    .withMessage('Loan amount must be between 1,000 and 1,000,000'),
  body('repaymentDuration')
    .isInt({ min: 1, max: 60 })
    .withMessage('Repayment duration must be between 1 and 60 months'),
  body('repaymentMethod')
    .isIn(Object.values(RepaymentMethod))
    .withMessage('Invalid repayment method'),
  body('advancePayment')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Advance payment must be a positive number'),
  body('interestRate')
    .optional()
    .isFloat({ min: 0, max: 50 })
    .withMessage('Interest rate must be between 0 and 50 percent'),
];

const validateQueryParams = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('status')
    .optional()
    .isIn(Object.values(LoanApplicationStatus))
    .withMessage('Invalid status'),
  query('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
  query('memberId')
    .optional()
    .isUUID()
    .withMessage('Invalid member ID format'),
  query('sortBy')
    .optional()
    .isIn(['appliedAt', 'appliedAmount', 'status', 'loanDate', 'loanAmount'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
];

// All routes require authentication
router.use(authenticate);

// Loan Application Routes

// GET /api/loan-applications - Get loan applications (role-based filtering)
router.get(
  '/loan-applications',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateQueryParams,
  handleValidationErrors,
  loanController.getLoanApplications.bind(loanController)
);

// POST /api/loan-applications - Create new loan application
router.post(
  '/loan-applications',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateCreateLoanApplication,
  handleValidationErrors,
  loanController.createLoanApplication.bind(loanController)
);

// GET /api/loan-applications/:id - Get loan application details
router.get(
  '/loan-applications/:id',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateLoanApplicationId,
  handleValidationErrors,
  loanController.getLoanApplicationById.bind(loanController)
);

// PUT /api/loan-applications/:id - Update loan application
router.put(
  '/loan-applications/:id',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateLoanApplicationId,
  validateUpdateLoanApplication,
  handleValidationErrors,
  loanController.updateLoanApplication.bind(loanController)
);

// PUT /api/loan-applications/:id/approve - Approve loan application (manager only)
router.put(
  '/loan-applications/:id/approve',
  requireRole([UserRole.admin, UserRole.manager]),
  validateLoanApplicationId,
  handleValidationErrors,
  loanController.approveLoanApplication.bind(loanController)
);

// PUT /api/loan-applications/:id/reject - Reject loan application (manager only)
router.put(
  '/loan-applications/:id/reject',
  requireRole([UserRole.admin, UserRole.manager]),
  validateLoanApplicationId,
  validateRejectApplication,
  handleValidationErrors,
  loanController.rejectLoanApplication.bind(loanController)
);

// Loan Routes

// GET /api/loans - Get active loans
router.get(
  '/loans',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateQueryParams,
  handleValidationErrors,
  loanController.getLoans.bind(loanController)
);

// GET /api/loans/:id - Get loan details
router.get(
  '/loans/:id',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateLoanId,
  handleValidationErrors,
  loanController.getLoanById.bind(loanController)
);

// GET /api/loans/:id/installments - Get loan installment schedule
router.get(
  '/loans/:id/installments',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateLoanId,
  handleValidationErrors,
  loanController.getLoanInstallments.bind(loanController)
);

// POST /api/loans/calculator - Loan calculation endpoint
router.post(
  '/loans/calculate',
  requireRole([UserRole.admin, UserRole.manager, UserRole.field_officer]),
  validateLoanCalculation,
  handleValidationErrors,
  loanController.calculateLoan.bind(loanController)
);

export default router;
