import { Router } from 'express';
import { userController } from '@/controllers/userController';
import { authenticate, requireRole } from '@/middleware/auth';
import { body, param, query, validationResult } from 'express-validator';
import { handleValidationErrors } from '@/middleware/validation';
import { ResponseUtil } from '@/utils/response';
import { UserRole } from '@prisma/client';

const router = Router();

// Validation middleware
const validateUserId = [
  param('id')
    .isUUID()
    .withMessage('Invalid user ID format'),
];

const validateCreateUser = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('role')
    .optional()
    .isIn(Object.values(UserRole))
    .withMessage('Invalid role'),
  body('memberId')
    .optional()
    .isLength({ min: 3, max: 20 })
    .withMessage('Member ID must be between 3 and 20 characters'),
  body('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
];

const validateUpdateUser = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('role')
    .optional()
    .isIn(Object.values(UserRole))
    .withMessage('Invalid role'),
  body('memberId')
    .optional()
    .isLength({ min: 3, max: 20 })
    .withMessage('Member ID must be between 3 and 20 characters'),
  body('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
];

const validateResetPassword = [
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
];

const validateBulkImport = [
  body('users')
    .isArray({ min: 1 })
    .withMessage('Users array is required and cannot be empty'),
  body('users.*.name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Each user must have a name between 2 and 100 characters'),
  body('users.*.email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Each user must have a valid email'),
  body('users.*.password')
    .isLength({ min: 8 })
    .withMessage('Each user must have a password of at least 8 characters'),
];

const validateQueryParams = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('role')
    .optional()
    .isIn(Object.values(UserRole))
    .withMessage('Invalid role'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('branchId')
    .optional()
    .isUUID()
    .withMessage('Invalid branch ID format'),
  query('sortBy')
    .optional()
    .isIn(['name', 'email', 'role', 'createdAt', 'lastLoginAt'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
];

// All user management routes require admin role
router.use(authenticate);
router.use(requireRole([UserRole.admin]));

// GET /api/users - Get all users with pagination and filtering
router.get(
  '/',
  validateQueryParams,
  handleValidationErrors,
  userController.getUsers.bind(userController)
);

// POST /api/users - Create new user
router.post(
  '/',
  validateCreateUser,
  handleValidationErrors,
  userController.createUser.bind(userController)
);

// GET /api/users/stats - Get user statistics
router.get(
  '/stats',
  userController.getUserStats.bind(userController)
);

// GET /api/users/export - Export users data
router.get(
  '/export',
  validateQueryParams,
  handleValidationErrors,
  userController.exportUsers.bind(userController)
);

// POST /api/users/bulk-import - Bulk import users
router.post(
  '/bulk-import',
  validateBulkImport,
  handleValidationErrors,
  userController.bulkImportUsers.bind(userController)
);

// GET /api/users/:id - Get user by ID
router.get(
  '/:id',
  validateUserId,
  handleValidationErrors,
  userController.getUserById.bind(userController)
);

// PUT /api/users/:id - Update user
router.put(
  '/:id',
  validateUserId,
  validateUpdateUser,
  handleValidationErrors,
  userController.updateUser.bind(userController)
);

// DELETE /api/users/:id - Delete user
router.delete(
  '/:id',
  validateUserId,
  handleValidationErrors,
  userController.deleteUser.bind(userController)
);

// PUT /api/users/:id/reset-password - Reset user password
router.put(
  '/:id/reset-password',
  validateUserId,
  validateResetPassword,
  handleValidationErrors,
  userController.resetUserPassword.bind(userController)
);

// PUT /api/users/:id/toggle-status - Activate/deactivate user
router.put(
  '/:id/toggle-status',
  validateUserId,
  handleValidationErrors,
  userController.toggleUserStatus.bind(userController)
);

export default router;
