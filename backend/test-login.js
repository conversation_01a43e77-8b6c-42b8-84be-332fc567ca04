const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testLogin() {
  try {
    console.log('Testing login credentials...\n');
    
    const testCredentials = [
      { memberId: 'ADMIN001', password: 'admin123', name: '<PERSON><PERSON>' },
      { memberId: 'MGR001', password: 'manager123', name: '<PERSON><PERSON>' },
      { memberId: 'FO001', password: 'officer123', name: '<PERSON><PERSON>' },
      { memberId: 'MEM001', password: 'member123', name: '<PERSON><PERSON>' }
    ];
    
    for (const cred of testCredentials) {
      console.log(`Testing ${cred.name} (${cred.memberId})...`);
      
      // Find user by member ID
      const user = await prisma.user.findUnique({
        where: { memberId: cred.memberId }
      });
      
      if (!user) {
        console.log(`❌ User not found for member ID: ${cred.memberId}`);
        continue;
      }
      
      // Test password
      const isPasswordValid = await bcrypt.compare(cred.password, user.password);
      
      if (isPasswordValid) {
        console.log(`✅ Password correct for ${cred.name}`);
      } else {
        console.log(`❌ Password incorrect for ${cred.name}`);
        
        // Let's check what the stored password hash looks like
        console.log(`   Stored hash: ${user.password.substring(0, 20)}...`);
        
        // Try to hash the expected password and compare
        const testHash = await bcrypt.hash(cred.password, 12);
        console.log(`   Test hash: ${testHash.substring(0, 20)}...`);
      }
      
      console.log('');
    }
    
  } catch (error) {
    console.error('Error testing login:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testLogin();
