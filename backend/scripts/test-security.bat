@echo off
setlocal enabledelayedexpansion

REM Security Testing Script for Sonali App (Windows)
REM This script tests various security features of the authentication system

set API_BASE_URL=http://localhost:3001/api
set TEST_EMAIL=<EMAIL>
set TEST_MEMBER_ID=MEM001
set TEST_PASSWORD=TestPass123!

echo ========================================
echo SONALI APP SECURITY TESTING
echo ========================================
echo.

echo [TEST] Testing basic connectivity...
curl -s -o nul -w "Health Check Status: %%{http_code}\n" %API_BASE_URL%/health
echo.

echo ========================================
echo AUTHENTICATION TESTS
echo ========================================
echo.

echo [TEST] Login with Member ID...
curl -s -X POST -H "Content-Type: application/json" ^
  -d "{\"identifier\":\"ADMIN001\",\"password\":\"admin123\",\"rememberMe\":false}" ^
  -w "Status: %%{http_code}\n" -o nul ^
  %API_BASE_URL%/auth/login
echo.

echo [TEST] Login with email...
curl -s -X POST -H "Content-Type: application/json" ^
  -d "{\"identifier\":\"<EMAIL>\",\"password\":\"admin123\",\"rememberMe\":false}" ^
  -w "Status: %%{http_code}\n" -o nul ^
  %API_BASE_URL%/auth/login
echo.

echo [TEST] Login with invalid credentials...
curl -s -X POST -H "Content-Type: application/json" ^
  -d "{\"identifier\":\"invalid\",\"password\":\"invalid\"}" ^
  -w "Status: %%{http_code}\n" -o nul ^
  %API_BASE_URL%/auth/login
echo.

echo ========================================
echo RATE LIMITING TESTS
echo ========================================
echo.

echo [TEST] Testing login rate limiting (5 failed attempts)...
for /L %%i in (1,1,6) do (
  echo Attempt %%i:
  curl -s -X POST -H "Content-Type: application/json" ^
    -d "{\"identifier\":\"invalid\",\"password\":\"invalid\"}" ^
    -w "Status: %%{http_code}\n" -o nul ^
    %API_BASE_URL%/auth/login
  timeout /t 1 /nobreak >nul
)
echo.

echo ========================================
echo INPUT VALIDATION TESTS
echo ========================================
echo.

echo [TEST] SQL injection attempt in identifier...
curl -s -X POST -H "Content-Type: application/json" ^
  -d "{\"identifier\":\"<EMAIL> OR 1=1\",\"password\":\"admin123\"}" ^
  -w "Status: %%{http_code}\n" -o nul ^
  %API_BASE_URL%/auth/login
echo.

echo [TEST] XSS attempt in identifier...
curl -s -X POST -H "Content-Type: application/json" ^
  -d "{\"identifier\":\"^<script^>alert('xss')^</script^>\",\"password\":\"admin123\"}" ^
  -w "Status: %%{http_code}\n" -o nul ^
  %API_BASE_URL%/auth/login
echo.

echo ========================================
echo PASSWORD RESET TESTS
echo ========================================
echo.

echo [TEST] Forgot password with valid email...
curl -s -X POST -H "Content-Type: application/json" ^
  -d "{\"identifier\":\"<EMAIL>\"}" ^
  -w "Status: %%{http_code}\n" -o nul ^
  %API_BASE_URL%/auth/forgot-password
echo.

echo [TEST] Forgot password with invalid email...
curl -s -X POST -H "Content-Type: application/json" ^
  -d "{\"identifier\":\"<EMAIL>\"}" ^
  -w "Status: %%{http_code}\n" -o nul ^
  %API_BASE_URL%/auth/forgot-password
echo.

echo ========================================
echo SESSION MANAGEMENT TESTS
echo ========================================
echo.

echo [TEST] Getting valid access token...
for /f "tokens=*" %%a in ('curl -s -X POST -H "Content-Type: application/json" -d "{\"identifier\":\"<EMAIL>\",\"password\":\"admin123\"}" %API_BASE_URL%/auth/login') do set login_response=%%a

REM Extract access token (simplified - in real scenario you'd use a JSON parser)
echo Login response received
echo.

echo [TEST] Testing logout...
curl -s -X POST -H "Content-Type: application/json" ^
  -H "Authorization: Bearer dummy_token" ^
  -w "Status: %%{http_code}\n" -o nul ^
  %API_BASE_URL%/auth/logout
echo.

echo ========================================
echo SECURITY HEADERS TESTS
echo ========================================
echo.

echo [TEST] Checking security headers...
curl -s -I %API_BASE_URL%/health | findstr /i "x-content-type-options x-frame-options x-xss-protection"
echo.

echo ========================================
echo CORS TESTS
echo ========================================
echo.

echo [TEST] CORS preflight request...
curl -s -X OPTIONS -H "Origin: https://www.sonalibd.org" ^
  -w "Status: %%{http_code}\n" -o nul ^
  %API_BASE_URL%/auth/login
echo.

echo ========================================
echo SECURITY TESTING COMPLETED
echo ========================================
echo.
echo All security tests have been executed.
echo Review the results above for any failed tests.
echo For production deployment, ensure all tests pass.
echo.
pause
