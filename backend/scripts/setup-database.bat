@echo off
setlocal enabledelayedexpansion

echo 🚀 Setting up Sonali App Database...

REM Check if .env file exists
if not exist ".env" (
    echo [ERROR] .env file not found! Please copy .env.example to .env and configure it.
    pause
    exit /b 1
)

echo [INFO] Checking environment configuration...

REM Generate Prisma client
echo [INFO] Generating Prisma client...
call npm run db:generate
if errorlevel 1 (
    echo [ERROR] Failed to generate Prisma client
    pause
    exit /b 1
)

REM Run migrations
echo [INFO] Running database migrations...
call npm run db:migrate
if errorlevel 1 (
    echo [ERROR] Failed to run migrations. Please check your database connection.
    echo [WARNING] Make sure MySQL is running and .env file has correct credentials.
    pause
    exit /b 1
)

REM Seed the database
echo [INFO] Seeding database with initial data...
call npm run db:seed
if errorlevel 1 (
    echo [ERROR] Failed to seed database
    pause
    exit /b 1
)

echo [INFO] ✅ Database setup completed successfully!
echo.
echo 🎉 Your Sonali App database is ready!
echo.
echo 📋 Default Login Credentials:
echo    Admin: <EMAIL> / admin123
echo    Manager: <EMAIL> / manager123
echo    Field Officer: <EMAIL> / officer123
echo.
echo 🔧 Useful commands:
echo    npm run db:studio    - Open Prisma Studio (Database GUI)
echo    npm run db:migrate   - Run new migrations
echo    npm run db:seed      - Re-seed the database
echo    npm run db:reset     - Reset database (WARNING: This will delete all data)
echo.
pause
