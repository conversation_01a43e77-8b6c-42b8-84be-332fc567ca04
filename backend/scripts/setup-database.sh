#!/bin/bash

# Database Setup Script for Sonali App
# This script sets up the MySQL database and runs migrations

set -e

echo "🚀 Setting up Sonali App Database..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found! Please copy .env.example to .env and configure it."
    exit 1
fi

# Load environment variables
source .env

# Check if MySQL is running
print_status "Checking MySQL connection..."
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1; then
    print_error "Cannot connect to MySQL. Please ensure MySQL is running and credentials are correct."
    print_warning "Make sure to update your .env file with correct database credentials."
    exit 1
fi

print_status "MySQL connection successful!"

# Create database if it doesn't exist
print_status "Creating database if it doesn't exist..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$DB_NAME\`;"

print_status "Database '$DB_NAME' is ready!"

# Generate Prisma client
print_status "Generating Prisma client..."
npm run db:generate

# Run migrations
print_status "Running database migrations..."
npm run db:migrate

# Seed the database
print_status "Seeding database with initial data..."
npm run db:seed

print_status "✅ Database setup completed successfully!"
echo ""
echo "🎉 Your Sonali App database is ready!"
echo ""
echo "📋 Default Login Credentials:"
echo "   Admin: <EMAIL> / admin123"
echo "   Manager: <EMAIL> / manager123"
echo "   Field Officer: <EMAIL> / officer123"
echo ""
echo "🔧 Useful commands:"
echo "   npm run db:studio    - Open Prisma Studio (Database GUI)"
echo "   npm run db:migrate   - Run new migrations"
echo "   npm run db:seed      - Re-seed the database"
echo "   npm run db:reset     - Reset database (WARNING: This will delete all data)"
