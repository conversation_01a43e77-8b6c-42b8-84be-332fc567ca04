{"root": ["./src/App.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/Layout/Header.tsx", "./src/components/Layout/Layout.tsx", "./src/components/auth/ProtectedRoute.tsx", "./src/components/auth/index.ts", "./src/components/dashboard/ActivityTimeline.tsx", "./src/components/dashboard/AdminDashboard.tsx", "./src/components/dashboard/BranchManagerDashboard.tsx", "./src/components/dashboard/FieldOfficerDashboard.tsx", "./src/components/dashboard/MemberDashboard.tsx", "./src/components/dashboard/MetricCard.tsx", "./src/components/dashboard/PerformanceChart.tsx", "./src/components/dashboard/QuickActionButton.tsx", "./src/components/dashboard/index.ts", "./src/components/members/MemberDetails.tsx", "./src/components/members/MemberList.tsx", "./src/components/members/MemberRegistrationForm.tsx", "./src/components/members/MemberSearch.tsx", "./src/components/members/PhotoUpload.tsx", "./src/components/members/index.ts", "./src/components/ui/Alert.tsx", "./src/components/ui/Button.tsx", "./src/components/ui/Card.tsx", "./src/components/ui/Form.tsx", "./src/components/ui/Input.tsx", "./src/components/ui/Loading.tsx", "./src/components/ui/Modal.tsx", "./src/components/ui/Navigation.tsx", "./src/components/ui/Sidebar.tsx", "./src/components/ui/Table.tsx", "./src/components/ui/ThemeToggle.tsx", "./src/components/ui/index.ts", "./src/components/ui/__tests__/Button.test.tsx", "./src/components/ui/__tests__/Input.test.tsx", "./src/contexts/AuthContext.tsx", "./src/contexts/ThemeContext.tsx", "./src/contexts/__tests__/AuthContext.test.tsx", "./src/hooks/useApiQuery.ts", "./src/pages/ComponentShowcase.tsx", "./src/pages/Dashboard.tsx", "./src/pages/Home.tsx", "./src/pages/Login.tsx", "./src/pages/MemberManagement.tsx", "./src/pages/Unauthorized.tsx", "./src/pages/__tests__/Login.test.tsx", "./src/services/authService.ts", "./src/services/branchService.ts", "./src/services/dashboardService.ts", "./src/services/financialService.ts", "./src/services/index.ts", "./src/services/installmentService.ts", "./src/services/loanService.ts", "./src/services/memberService.ts", "./src/services/realtimeService.ts", "./src/services/uploadService.ts", "./src/services/userService.ts", "./src/test-utils/accessibility.ts", "./src/test-utils/setup.ts", "./src/types/index.ts", "./src/utils/api.ts", "./src/utils/cn.ts", "./src/utils/constants.ts"], "errors": true, "version": "5.8.3"}