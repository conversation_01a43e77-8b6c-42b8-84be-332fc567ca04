// Simple test to check frontend login API call
const testLogin = async () => {
  try {
    console.log('Testing frontend login API call...');
    
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        identifier: 'ADMIN001',
        password: 'admin123',
        rememberMe: false
      })
    });
    
    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      console.log('✅ Login successful!');
      console.log('User:', data.data.user.name);
      console.log('Role:', data.data.user.role);
      console.log('Member ID:', data.data.user.memberId);
    } else {
      console.log('❌ Login failed:', data.error || data.message);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
};

testLogin();
