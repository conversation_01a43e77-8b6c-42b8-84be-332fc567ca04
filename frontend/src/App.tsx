import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { Layout } from './components/Layout/Layout';
import { Home } from './pages/Home';
import { Login } from './pages/Login';
import { Unauthorized } from './pages/Unauthorized';
import { Dashboard } from './pages/Dashboard';
import { ComponentShowcase } from './pages/ComponentShowcase';
import { MemberRegistration } from './pages/MemberRegistration';
import { LoanApplication } from './pages/LoanApplication';
import { InstallmentCollection } from './pages/InstallmentCollection';
import { LoanCalculator } from './pages/LoanCalculator';
import { LoanApproval } from './pages/LoanApproval';
import { BranchFinancials } from './pages/BranchFinancials';
import { ProtectedRoute } from './components/auth';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <QueryClientProvider client={queryClient}>
          <Router>
            <Routes>
              {/* Redirect root to login */}
              <Route path="/" element={<Navigate to="/login" replace />} />

              {/* Auth routes without layout */}
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<div className="min-h-screen flex items-center justify-center bg-secondary-50 dark:bg-secondary-900"><div className="text-center"><h1 className="text-2xl font-bold mb-4">Register Page</h1><p>Coming Soon</p></div></div>} />

              {/* Routes with layout */}
              <Route path="/*" element={
                <Layout>
                  <Routes>
                    <Route path="/home" element={<Home />} />
                    <Route path="/unauthorized" element={<Unauthorized />} />
                    <Route path="/components" element={<ComponentShowcase />} />

                    {/* Protected Routes */}
                    <Route
                      path="/dashboard"
                      element={
                        <ProtectedRoute>
                          <Dashboard />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/admin/dashboard"
                      element={
                        <ProtectedRoute requiredRoles="admin">
                          <Dashboard />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/manager/dashboard"
                      element={
                        <ProtectedRoute requiredRoles="manager">
                          <Dashboard />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/field-officer/dashboard"
                      element={
                        <ProtectedRoute requiredRoles="field_officer">
                          <Dashboard />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/member/dashboard"
                      element={
                        <ProtectedRoute requiredRoles="member">
                          <Dashboard />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/profile"
                      element={
                        <ProtectedRoute>
                          <div>Profile Page (Coming Soon)</div>
                        </ProtectedRoute>
                      }
                    />

                    {/* Form Pages */}
                    <Route
                      path="/members/register"
                      element={
                        <ProtectedRoute requiredRoles={["admin", "manager", "field_officer"]}>
                          <MemberRegistration />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/loans/apply"
                      element={
                        <ProtectedRoute requiredRoles={["admin", "manager", "field_officer"]}>
                          <LoanApplication />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/collections"
                      element={
                        <ProtectedRoute requiredRoles={["admin", "manager", "field_officer"]}>
                          <InstallmentCollection />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/calculator"
                      element={
                        <ProtectedRoute>
                          <LoanCalculator />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/loans/approvals"
                      element={
                        <ProtectedRoute requiredRoles={["admin", "manager"]}>
                          <LoanApproval />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/branch/financials"
                      element={
                        <ProtectedRoute requiredRoles={["admin", "manager"]}>
                          <BranchFinancials />
                        </ProtectedRoute>
                      }
                    />

                    {/* History and Detail Pages */}
                    <Route
                      path="/collections/history/:memberId"
                      element={
                        <ProtectedRoute requiredRoles={["admin", "manager", "field_officer"]}>
                          <div className="text-center py-12">
                            <h1 className="text-2xl font-bold mb-4">Collection History</h1>
                            <p>Detailed collection history page coming soon</p>
                          </div>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/loans/applications/history"
                      element={
                        <ProtectedRoute requiredRoles={["admin", "manager"]}>
                          <div className="text-center py-12">
                            <h1 className="text-2xl font-bold mb-4">Loan Application History</h1>
                            <p>Detailed application history page coming soon</p>
                          </div>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/branch/transactions/history"
                      element={
                        <ProtectedRoute requiredRoles={["admin", "manager"]}>
                          <div className="text-center py-12">
                            <h1 className="text-2xl font-bold mb-4">Transaction History</h1>
                            <p>Detailed transaction history page coming soon</p>
                          </div>
                        </ProtectedRoute>
                      }
                    />
                  </Routes>
                </Layout>
              } />
            </Routes>
          </Router>
        </QueryClientProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
