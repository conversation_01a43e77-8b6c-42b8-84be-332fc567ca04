import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { 
  authService, 
  userService, 
  branchService, 
  loanService, 
  installmentService, 
  financialService,
  MemberService,
  DashboardService 
} from '../services';
import { 
  User, 
  UserListQuery, 
  Branch, 
  BranchListQuery,
  Member, 
  MemberListQuery,
  LoanApplication,
  LoanApplicationListQuery,
  Loan,
  LoanListQuery,
  Installment,
  InstallmentListQuery,
  Transaction,
  TransactionListQuery,
  UserRole
} from '../types';

// Query Keys
export const queryKeys = {
  // Auth
  profile: ['auth', 'profile'] as const,
  sessions: ['auth', 'sessions'] as const,
  
  // Users
  users: (query?: UserListQuery) => ['users', query] as const,
  user: (id: string) => ['users', id] as const,
  userStats: ['users', 'stats'] as const,
  
  // Branches
  branches: (query?: BranchListQuery) => ['branches', query] as const,
  branch: (id: string) => ['branches', id] as const,
  branchStats: (id: string) => ['branches', id, 'stats'] as const,
  branchMembers: (id: string, query?: any) => ['branches', id, 'members', query] as const,
  
  // Members
  members: (query?: MemberListQuery) => ['members', query] as const,
  member: (id: string) => ['members', id] as const,
  memberSearch: (query: string) => ['members', 'search', query] as const,
  
  // Loans
  loanApplications: (query?: LoanApplicationListQuery) => ['loan-applications', query] as const,
  loanApplication: (id: string) => ['loan-applications', id] as const,
  loans: (query?: LoanListQuery) => ['loans', query] as const,
  loan: (id: string) => ['loans', id] as const,
  loanInstallments: (id: string) => ['loans', id, 'installments'] as const,
  
  // Installments
  installments: (query?: InstallmentListQuery) => ['installments', query] as const,
  pendingInstallments: (query?: any) => ['installments', 'pending', query] as const,
  overdueInstallments: (query?: any) => ['installments', 'overdue', query] as const,
  installmentStats: (branchId?: string) => ['installments', 'stats', branchId] as const,
  
  // Financial
  transactions: (query?: TransactionListQuery) => ['transactions', query] as const,
  financialReports: (period: string, branchId?: string) => ['financial', 'reports', period, branchId] as const,
  branchSummary: (period: string) => ['financial', 'branch-summary', period] as const,
  
  // Dashboard
  dashboard: (role: UserRole, userId?: string, branchId?: string) => ['dashboard', role, userId, branchId] as const,
} as const;

// Auth Hooks
export const useProfile = (options?: UseQueryOptions<User>) => {
  return useQuery({
    queryKey: queryKeys.profile,
    queryFn: () => authService.getProfile(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

export const useActiveSessions = (options?: UseQueryOptions<any[]>) => {
  return useQuery({
    queryKey: queryKeys.sessions,
    queryFn: () => authService.getActiveSessions(),
    staleTime: 1 * 60 * 1000, // 1 minute
    ...options,
  });
};

// User Hooks
export const useUsers = (query?: UserListQuery, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.users(query),
    queryFn: () => userService.getUsers(query),
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

export const useUser = (id: string, options?: UseQueryOptions<User>) => {
  return useQuery({
    queryKey: queryKeys.user(id),
    queryFn: () => userService.getUserById(id),
    staleTime: 1 * 60 * 1000, // 1 minute
    enabled: !!id,
    ...options,
  });
};

export const useUserStats = (options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.userStats,
    queryFn: () => userService.getUserStats(),
    staleTime: 1 * 60 * 1000, // 1 minute
    ...options,
  });
};

// Branch Hooks
export const useBranches = (query?: BranchListQuery, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.branches(query),
    queryFn: () => branchService.getBranches(query),
    staleTime: 1 * 60 * 1000, // 1 minute
    ...options,
  });
};

export const useBranch = (id: string, options?: UseQueryOptions<Branch>) => {
  return useQuery({
    queryKey: queryKeys.branch(id),
    queryFn: () => branchService.getBranchById(id),
    staleTime: 1 * 60 * 1000, // 1 minute
    enabled: !!id,
    ...options,
  });
};

export const useBranchStats = (id: string, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.branchStats(id),
    queryFn: () => branchService.getBranchStats(id),
    staleTime: 30 * 1000, // 30 seconds
    enabled: !!id,
    ...options,
  });
};

// Member Hooks
export const useMembers = (query?: MemberListQuery, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.members(query),
    queryFn: () => MemberService.getMembers(query),
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

export const useMember = (id: string, options?: UseQueryOptions<Member>) => {
  return useQuery({
    queryKey: queryKeys.member(id),
    queryFn: () => MemberService.getMemberById(id),
    staleTime: 1 * 60 * 1000, // 1 minute
    enabled: !!id,
    ...options,
  });
};

export const useMemberSearch = (query: string, options?: UseQueryOptions<any[]>) => {
  return useQuery({
    queryKey: queryKeys.memberSearch(query),
    queryFn: () => MemberService.searchMembers({ query }),
    staleTime: 15 * 1000, // 15 seconds
    enabled: query.length >= 2,
    ...options,
  });
};

// Loan Hooks
export const useLoanApplications = (query?: LoanApplicationListQuery, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.loanApplications(query),
    queryFn: () => loanService.getLoanApplications(query),
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

export const useLoanApplication = (id: string, options?: UseQueryOptions<LoanApplication>) => {
  return useQuery({
    queryKey: queryKeys.loanApplication(id),
    queryFn: () => loanService.getLoanApplicationById(id),
    staleTime: 1 * 60 * 1000, // 1 minute
    enabled: !!id,
    ...options,
  });
};

export const useLoans = (query?: LoanListQuery, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.loans(query),
    queryFn: () => loanService.getLoans(query),
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

export const useLoan = (id: string, options?: UseQueryOptions<Loan>) => {
  return useQuery({
    queryKey: queryKeys.loan(id),
    queryFn: () => loanService.getLoanById(id),
    staleTime: 1 * 60 * 1000, // 1 minute
    enabled: !!id,
    ...options,
  });
};

// Installment Hooks
export const useInstallments = (query?: InstallmentListQuery, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.installments(query),
    queryFn: () => installmentService.getInstallments(query),
    staleTime: 15 * 1000, // 15 seconds (more real-time)
    ...options,
  });
};

export const usePendingInstallments = (query?: any, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.pendingInstallments(query),
    queryFn: () => installmentService.getPendingInstallments(query),
    staleTime: 15 * 1000, // 15 seconds
    ...options,
  });
};

export const useInstallmentStats = (branchId?: string, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.installmentStats(branchId),
    queryFn: () => installmentService.getInstallmentStats(branchId),
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

// Financial Hooks
export const useTransactions = (query?: TransactionListQuery, options?: UseQueryOptions<any>) => {
  return useQuery({
    queryKey: queryKeys.transactions(query),
    queryFn: () => financialService.getTransactions(query),
    staleTime: 30 * 1000, // 30 seconds
    ...options,
  });
};

export const useFinancialReports = (
  period: string = 'monthly', 
  branchId?: string, 
  options?: UseQueryOptions<any>
) => {
  return useQuery({
    queryKey: queryKeys.financialReports(period, branchId),
    queryFn: () => financialService.getFinancialReports(period as any, branchId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

// Dashboard Hooks
export const useDashboard = (
  role: UserRole, 
  userId?: string, 
  branchId?: string, 
  options?: UseQueryOptions<any>
) => {
  return useQuery({
    queryKey: queryKeys.dashboard(role, userId, branchId),
    queryFn: () => DashboardService.getDashboardByRole(role, userId, branchId),
    staleTime: 1 * 60 * 1000, // 1 minute
    ...options,
  });
};

// Mutation Hooks
export const useCreateUser = (options?: UseMutationOptions<User, Error, any>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data) => userService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: queryKeys.userStats });
    },
    ...options,
  });
};

export const useUpdateUser = (options?: UseMutationOptions<User, Error, { id: string; data: any }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }) => userService.updateUser(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: queryKeys.user(id) });
    },
    ...options,
  });
};

export const useCreateMember = (options?: UseMutationOptions<Member, Error, any>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data) => MemberService.createMember(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['members'] });
    },
    ...options,
  });
};

export const useCollectInstallment = (options?: UseMutationOptions<any, Error, { id: string; data: any }>) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }) => installmentService.collectInstallment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['installments'] });
      queryClient.invalidateQueries({ queryKey: ['loans'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
    ...options,
  });
};
