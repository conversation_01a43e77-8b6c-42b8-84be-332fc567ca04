import { apiClient } from '../utils/api';
import { API_ENDPOINTS, APP_CONFIG } from '../utils/constants';

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadResult {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}

export interface BulkUploadResult {
  successful: UploadResult[];
  failed: Array<{
    filename: string;
    error: string;
  }>;
  totalProcessed: number;
}

export interface UploadOptions {
  onProgress?: (progress: UploadProgress) => void;
  onSuccess?: (result: UploadResult) => void;
  onError?: (error: Error) => void;
  maxSize?: number;
  allowedTypes?: string[];
  additionalData?: Record<string, any>;
}

export class UploadService {
  private static validateFile(file: File, options: UploadOptions = {}): void {
    const maxSize = options.maxSize || APP_CONFIG.MAX_FILE_SIZE;
    const allowedTypes = options.allowedTypes || APP_CONFIG.SUPPORTED_FILE_TYPES;

    // Check file size
    if (file.size > maxSize) {
      throw new Error(`File size exceeds maximum limit of ${Math.round(maxSize / (1024 * 1024))}MB`);
    }

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      throw new Error(`File type ${file.type} is not supported. Allowed types: ${allowedTypes.join(', ')}`);
    }

    // Check if file is empty
    if (file.size === 0) {
      throw new Error('Cannot upload empty file');
    }
  }

  private static createProgressHandler(onProgress?: (progress: UploadProgress) => void) {
    return (progressEvent: any) => {
      if (onProgress && progressEvent.total) {
        const progress: UploadProgress = {
          loaded: progressEvent.loaded,
          total: progressEvent.total,
          percentage: Math.round((progressEvent.loaded * 100) / progressEvent.total),
        };
        onProgress(progress);
      }
    };
  }

  /**
   * Upload member photo
   */
  static async uploadMemberPhoto(
    memberId: string,
    file: File,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      // Validate file
      this.validateFile(file, {
        ...options,
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
        maxSize: 2 * 1024 * 1024, // 2MB for photos
      });

      const response = await apiClient.uploadFile<UploadResult>(
        API_ENDPOINTS.MEMBERS.UPLOAD_PHOTO(memberId),
        file,
        {
          onUploadProgress: this.createProgressHandler(options.onProgress),
          additionalData: options.additionalData,
        }
      );

      if (response.success && response.data) {
        options.onSuccess?.(response.data);
        return response.data;
      }

      throw new Error(response.error || 'Failed to upload member photo');
    } catch (error) {
      const uploadError = error instanceof Error ? error : new Error('Upload failed');
      options.onError?.(uploadError);
      throw uploadError;
    }
  }

  /**
   * Upload document
   */
  static async uploadDocument(
    file: File,
    documentType: 'nid' | 'passport' | 'license' | 'other',
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    try {
      // Validate file
      this.validateFile(file, {
        ...options,
        allowedTypes: ['image/jpeg', 'image/png', 'application/pdf'],
        maxSize: 5 * 1024 * 1024, // 5MB for documents
      });

      const response = await apiClient.uploadFile<UploadResult>(
        API_ENDPOINTS.UPLOADS.DOCUMENT,
        file,
        {
          onUploadProgress: this.createProgressHandler(options.onProgress),
          additionalData: {
            documentType,
            ...options.additionalData,
          },
        }
      );

      if (response.success && response.data) {
        options.onSuccess?.(response.data);
        return response.data;
      }

      throw new Error(response.error || 'Failed to upload document');
    } catch (error) {
      const uploadError = error instanceof Error ? error : new Error('Upload failed');
      options.onError?.(uploadError);
      throw uploadError;
    }
  }

  /**
   * Upload bulk import file (Excel/CSV)
   */
  static async uploadBulkImportFile(
    file: File,
    importType: 'members' | 'users' | 'transactions',
    options: UploadOptions = {}
  ): Promise<BulkUploadResult> {
    try {
      // Validate file
      this.validateFile(file, {
        ...options,
        allowedTypes: [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel',
          'text/csv',
        ],
        maxSize: 10 * 1024 * 1024, // 10MB for bulk files
      });

      const response = await apiClient.uploadFile<BulkUploadResult>(
        API_ENDPOINTS.UPLOADS.BULK_IMPORT,
        file,
        {
          onUploadProgress: this.createProgressHandler(options.onProgress),
          additionalData: {
            importType,
            ...options.additionalData,
          },
        }
      );

      if (response.success && response.data) {
        return response.data;
      }

      throw new Error(response.error || 'Failed to upload bulk import file');
    } catch (error) {
      const uploadError = error instanceof Error ? error : new Error('Upload failed');
      options.onError?.(uploadError);
      throw uploadError;
    }
  }

  /**
   * Upload multiple files
   */
  static async uploadMultipleFiles(
    files: File[],
    uploadType: 'documents' | 'photos',
    options: UploadOptions = {}
  ): Promise<BulkUploadResult> {
    const successful: UploadResult[] = [];
    const failed: Array<{ filename: string; error: string }> = [];

    for (const file of files) {
      try {
        let result: UploadResult;
        
        if (uploadType === 'photos') {
          // For photos, we need a member ID - this should be provided in additionalData
          const memberId = options.additionalData?.memberId;
          if (!memberId) {
            throw new Error('Member ID is required for photo uploads');
          }
          result = await this.uploadMemberPhoto(memberId, file, {
            ...options,
            onProgress: undefined, // Disable individual progress for bulk uploads
          });
        } else {
          result = await this.uploadDocument(file, 'other', {
            ...options,
            onProgress: undefined, // Disable individual progress for bulk uploads
          });
        }

        successful.push(result);
      } catch (error) {
        failed.push({
          filename: file.name,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return {
      successful,
      failed,
      totalProcessed: files.length,
    };
  }

  /**
   * Get upload progress for a file
   */
  static createUploadTracker(): {
    progress: UploadProgress | null;
    onProgress: (progress: UploadProgress) => void;
    reset: () => void;
  } {
    let progress: UploadProgress | null = null;

    return {
      get progress() {
        return progress;
      },
      onProgress: (newProgress: UploadProgress) => {
        progress = newProgress;
      },
      reset: () => {
        progress = null;
      },
    };
  }

  /**
   * Validate file before upload
   */
  static validateFileBeforeUpload(
    file: File,
    options: {
      maxSize?: number;
      allowedTypes?: string[];
    } = {}
  ): { isValid: boolean; error?: string } {
    try {
      this.validateFile(file, options);
      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Validation failed',
      };
    }
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file extension
   */
  static getFileExtension(filename: string): string {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
  }

  /**
   * Check if file type is image
   */
  static isImageFile(file: File): boolean {
    return file.type.startsWith('image/');
  }

  /**
   * Check if file type is document
   */
  static isDocumentFile(file: File): boolean {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];
    return documentTypes.includes(file.type);
  }

  /**
   * Create file preview URL
   */
  static createFilePreview(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!this.isImageFile(file)) {
        reject(new Error('File is not an image'));
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      reader.readAsDataURL(file);
    });
  }
}

export const uploadService = UploadService;
