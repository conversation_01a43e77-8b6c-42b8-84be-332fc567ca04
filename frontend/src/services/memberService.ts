import { apiClient } from '../utils/api';
import { API_ENDPOINTS } from '../utils/constants';
import {
  Member,
  CreateMemberData,
  UpdateMemberData,
  MemberListQuery,
  MemberSearchQuery,
  MemberSearchResult,
  PaginatedResponse,
  ApiResponse
} from '../types';

/**
 * Member Service
 * Handles all member-related API calls
 */
export class MemberService {
  
  /**
   * Get paginated list of members
   */
  static async getMembers(query: MemberListQuery = {}): Promise<PaginatedResponse<Member>> {
    const response = await apiClient.get<PaginatedResponse<Member>>(
      API_ENDPOINTS.MEMBERS.LIST,
      {
        params: query,
        cache: { ttl: 30000 }, // Cache for 30 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch members');
  }

  /**
   * Search members with autocomplete
   */
  static async searchMembers(query: MemberSearchQuery): Promise<MemberSearchResult[]> {
    const response = await apiClient.get<MemberSearchResult[]>(
      API_ENDPOINTS.MEMBERS.SEARCH,
      {
        params: query,
        cache: { ttl: 15000 }, // Cache for 15 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to search members');
  }

  /**
   * Get member by ID
   */
  static async getMemberById(id: string): Promise<Member> {
    const response = await apiClient.get<Member>(
      API_ENDPOINTS.MEMBERS.GET(id),
      {
        cache: { ttl: 60000 }, // Cache for 1 minute
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch member');
  }

  /**
   * Create new member
   */
  static async createMember(memberData: CreateMemberData): Promise<Member> {
    const response = await apiClient.post<Member>(
      API_ENDPOINTS.MEMBERS.CREATE,
      memberData
    );

    if (response.success && response.data) {
      // Clear members cache after creating new member
      apiClient.removeCacheEntry(API_ENDPOINTS.MEMBERS.LIST);
      return response.data;
    }

    throw new Error(response.error || 'Failed to create member');
  }

  /**
   * Update member
   */
  static async updateMember(id: string, memberData: UpdateMemberData): Promise<Member> {
    const response = await apiClient.put<Member>(
      API_ENDPOINTS.MEMBERS.UPDATE(id),
      memberData
    );

    if (response.success && response.data) {
      // Clear relevant caches
      apiClient.removeCacheEntry(API_ENDPOINTS.MEMBERS.LIST);
      apiClient.removeCacheEntry(API_ENDPOINTS.MEMBERS.GET(id));
      return response.data;
    }

    throw new Error(response.error || 'Failed to update member');
  }

  /**
   * Delete member (soft delete)
   */
  static async deleteMember(id: string): Promise<void> {
    const response = await apiClient.delete(API_ENDPOINTS.MEMBERS.DELETE(id));

    if (!response.success) {
      throw new Error(response.error || 'Failed to delete member');
    }

    // Clear relevant caches
    apiClient.removeCacheEntry(API_ENDPOINTS.MEMBERS.LIST);
    apiClient.removeCacheEntry(API_ENDPOINTS.MEMBERS.GET(id));
  }

  /**
   * Upload member photo
   */
  static async uploadPhoto(memberId: string, file: File): Promise<{ photoUrl: string }> {
    const response = await apiClient.uploadFile<{ photoUrl: string }>(
      API_ENDPOINTS.MEMBERS.UPLOAD_PHOTO(memberId),
      file,
      {
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          console.log(`Upload Progress: ${percentCompleted}%`);
        },
      }
    );

    if (response.success && response.data) {
      // Clear member cache to refresh photo
      apiClient.removeCacheEntry(API_ENDPOINTS.MEMBERS.GET(memberId));
      return response.data;
    }

    throw new Error(response.error || 'Failed to upload member photo');
  }

  /**
   * Generate member ID
   */
  static async generateMemberId(branchId?: string): Promise<string> {
    try {
      const params = branchId ? `?branchId=${branchId}` : '';
      const response = await api.get(`/members/generate-id${params}`);
      return response.data.memberId;
    } catch (error) {
      console.error('Error generating member ID:', error);
      throw error;
    }
  }

  /**
   * Export members to Excel
   */
  static async exportMembers(query: MemberListQuery = {}): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      
      if (query.search) params.append('search', query.search);
      if (query.branchId) params.append('branchId', query.branchId);
      if (query.isActive !== undefined) params.append('isActive', query.isActive.toString());

      const response = await api.get(`/members/export?${params.toString()}`, {
        responseType: 'blob',
      });
      
      return response.data;
    } catch (error) {
      console.error('Error exporting members:', error);
      throw error;
    }
  }

  /**
   * Bulk update members
   */
  static async bulkUpdateMembers(memberIds: string[], updateData: UpdateMemberData): Promise<void> {
    try {
      await api.put('/members/bulk-update', {
        memberIds,
        updateData,
      });
    } catch (error) {
      console.error('Error bulk updating members:', error);
      throw error;
    }
  }

  /**
   * Get member statistics
   */
  static async getMemberStats(branchId?: string): Promise<{
    total: number;
    active: number;
    inactive: number;
    thisMonth: number;
  }> {
    try {
      const params = branchId ? `?branchId=${branchId}` : '';
      const response = await api.get(`/members/stats${params}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching member stats:', error);
      throw error;
    }
  }

  /**
   * Validate member ID uniqueness
   */
  static async validateMemberId(memberId: string, excludeId?: string): Promise<boolean> {
    try {
      const params = new URLSearchParams();
      params.append('memberId', memberId);
      if (excludeId) params.append('excludeId', excludeId);

      const response = await api.get(`/members/validate-id?${params.toString()}`);
      return response.data.isValid;
    } catch (error) {
      console.error('Error validating member ID:', error);
      return false;
    }
  }

  /**
   * Validate NID uniqueness
   */
  static async validateNid(nidNumber: string, excludeId?: string): Promise<boolean> {
    try {
      const params = new URLSearchParams();
      params.append('nidNumber', nidNumber);
      if (excludeId) params.append('excludeId', excludeId);

      const response = await api.get(`/members/validate-nid?${params.toString()}`);
      return response.data.isValid;
    } catch (error) {
      console.error('Error validating NID:', error);
      return false;
    }
  }

  /**
   * Print member card
   */
  static async printMemberCard(memberId: string): Promise<Blob> {
    try {
      const response = await api.get(`/members/${memberId}/card`, {
        responseType: 'blob',
      });
      
      return response.data;
    } catch (error) {
      console.error('Error printing member card:', error);
      throw error;
    }
  }

  /**
   * Get members for reference selection
   */
  static async getMembersForReference(branchId?: string, excludeId?: string): Promise<MemberSearchResult[]> {
    try {
      const params = new URLSearchParams();
      if (branchId) params.append('branchId', branchId);
      if (excludeId) params.append('excludeId', excludeId);

      const response = await api.get(`/members/for-reference?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching members for reference:', error);
      throw error;
    }
  }
}

export default MemberService;
