import { apiClient } from '../utils/api';
import { API_ENDPOINTS } from '../utils/constants';
import {
  FieldOfficerDashboardData,
  BranchManagerDashboardData,
  AdminDashboardData,
  MemberDashboardData,
  ActivityItem,
  InstallmentSummary,
  UserRole
} from '../types';

/**
 * Dashboard Service
 * Handles all dashboard-related API calls for different user roles
 */
export class DashboardService {
  
  /**
   * Get Field Officer Dashboard Data
   */
  static async getFieldOfficerDashboard(): Promise<FieldOfficerDashboardData> {
    try {
      const response = await apiClient.get(API_ENDPOINTS.DASHBOARD.FIELD_OFFICER);
      return response.data;
    } catch (error) {
      console.error('Error fetching field officer dashboard:', error);
      // Return mock data for development
      return this.getMockFieldOfficerData();
    }
  }

  /**
   * Get Branch Manager Dashboard Data
   */
  static async getBranchManagerDashboard(): Promise<BranchManagerDashboardData> {
    try {
      const response = await apiClient.get(API_ENDPOINTS.DASHBOARD.MANAGER);
      return response.data;
    } catch (error) {
      console.error('Error fetching branch manager dashboard:', error);
      // Return mock data for development
      return this.getMockBranchManagerData();
    }
  }

  /**
   * Get Admin Dashboard Data
   */
  static async getAdminDashboard(): Promise<AdminDashboardData> {
    try {
      const response = await apiClient.get(API_ENDPOINTS.DASHBOARD.ADMIN);
      return response.data;
    } catch (error) {
      console.error('Error fetching admin dashboard:', error);
      // Return mock data for development
      return this.getMockAdminData();
    }
  }

  /**
   * Get Member Dashboard Data
   */
  static async getMemberDashboard(): Promise<MemberDashboardData> {
    try {
      const response = await apiClient.get(API_ENDPOINTS.DASHBOARD.MEMBER);
      return response.data;
    } catch (error) {
      console.error('Error fetching member dashboard:', error);
      // Return mock data for development
      return this.getMockMemberData();
    }
  }

  /**
   * Get Recent Activities for any role
   */
  static async getRecentActivities(limit: number = 10): Promise<ActivityItem[]> {
    try {
      const response = await apiClient.get(`${API_ENDPOINTS.DASHBOARD.ACTIVITIES}?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      return this.getMockActivities();
    }
  }

  /**
   * Get Upcoming Collections for Field Officers
   */
  static async getUpcomingCollections(): Promise<InstallmentSummary[]> {
    try {
      const response = await apiClient.get(API_ENDPOINTS.DASHBOARD.UPCOMING_COLLECTIONS);
      return response.data;
    } catch (error) {
      console.error('Error fetching upcoming collections:', error);
      return this.getMockUpcomingCollections();
    }
  }

  // Mock data methods for development
  private static getMockFieldOfficerData(): FieldOfficerDashboardData {
    return {
      metrics: {
        totalMembers: 45,
        totalCollections: 125000,
        monthlyTarget: 150000,
        achievementPercentage: 83.3,
        pendingInstallments: 12,
        overdueInstallments: 3
      },
      recentActivities: this.getMockActivities(),
      quickActions: [
        { id: '1', label: 'Member Registration', icon: 'user-plus', href: '/members/register', color: 'primary' },
        { id: '2', label: 'Loan Application', icon: 'file-text', href: '/loans/apply', color: 'success' },
        { id: '3', label: 'Installment Collection', icon: 'credit-card', href: '/collections', color: 'warning' },
        { id: '4', label: 'Loan Calculator', icon: 'calculator', href: '/calculator', color: 'info' }
      ],
      upcomingCollections: this.getMockUpcomingCollections()
    };
  }

  private static getMockBranchManagerData(): BranchManagerDashboardData {
    return {
      metrics: {
        totalFieldOfficers: 8,
        totalActiveLoans: 156,
        totalMembers: 342,
        monthlyCollectionTarget: 1200000,
        collectedAmount: 980000,
        pendingInstallments: 45,
        overdueInstallments: 12,
        branchIncome: 1150000,
        branchExpenses: 280000
      },
      installmentStatus: {
        pending: 45,
        overdue: 12,
        collected: 189
      },
      fieldOfficerPerformance: [
        { id: '1', name: 'Rahul Ahmed', membersCount: 45, collectionsThisMonth: 125000, targetAchievement: 83.3 },
        { id: '2', name: 'Fatima Khan', membersCount: 52, collectionsThisMonth: 145000, targetAchievement: 96.7 },
        { id: '3', name: 'Mohammad Ali', membersCount: 38, collectionsThisMonth: 98000, targetAchievement: 65.3 }
      ],
      recentActivities: this.getMockActivities(),
      quickActions: [
        { id: '1', label: 'Loan Approval Dashboard', icon: 'check-circle', href: '/loans/approvals', color: 'success' },
        { id: '2', label: 'Branch Income/Expenditure', icon: 'trending-up', href: '/branch/financials', color: 'primary' },
        { id: '3', label: 'Member Registration', icon: 'user-plus', href: '/members/register', color: 'info' },
        { id: '4', label: 'Installment Collection', icon: 'credit-card', href: '/collections', color: 'warning' }
      ]
    };
  }

  private static getMockAdminData(): AdminDashboardData {
    return {
      metrics: {
        totalBranches: 12,
        totalUsers: 156,
        totalFieldOfficers: 89,
        totalMembers: 2847,
        totalActiveLoans: 1456,
        totalLoanAmount: 45600000,
        totalCollections: 12800000,
        systemUptime: '99.8%'
      },
      financialPerformance: [
        { period: 'Jan', collections: 1200000, disbursements: 2800000, members: 2650 },
        { period: 'Feb', collections: 1350000, disbursements: 2200000, members: 2720 },
        { period: 'Mar', collections: 1280000, disbursements: 2600000, members: 2847 }
      ],
      branchPerformance: [
        { id: '1', name: 'Dhaka Main Branch', managerId: '1', managerName: 'Karim Ahmed', totalMembers: 342, activeLoans: 156, collectionsThisMonth: 980000, performance: 87.5 },
        { id: '2', name: 'Chittagong Branch', managerId: '2', managerName: 'Rashida Begum', totalMembers: 298, activeLoans: 134, collectionsThisMonth: 850000, performance: 92.3 }
      ],
      recentActivities: this.getMockActivities(),
      quickActions: [
        { id: '1', label: 'User Management', icon: 'users', href: '/admin/users', color: 'primary' },
        { id: '2', label: 'Branch Management', icon: 'building', href: '/admin/branches', color: 'success' },
        { id: '3', label: 'Loan Approvals', icon: 'check-circle', href: '/loans/approvals', color: 'warning' },
        { id: '4', label: 'Branch Financials', icon: 'dollar-sign', href: '/branch/financials', color: 'info' }
      ],
      alerts: [
        { id: '1', type: 'warning', title: 'High Overdue Rate', message: 'Chittagong branch has 15% overdue rate', timestamp: '2024-01-15T10:30:00Z', isRead: false },
        { id: '2', type: 'info', title: 'Monthly Target Achieved', message: 'Dhaka Main branch achieved 105% of monthly target', timestamp: '2024-01-15T09:15:00Z', isRead: true }
      ]
    };
  }

  private static getMockMemberData(): MemberDashboardData {
    return {
      financialSummary: {
        savingsBalance: 25000,
        totalSavings: 45000,
        activeLoanAmount: 35000,
        totalLoanAmount: 50000,
        nextPaymentAmount: 4583,
        nextPaymentDate: '2024-02-15',
        creditScore: 750
      },
      activeLoans: [
        {
          id: '1',
          loanAmount: 50000,
          remainingAmount: 35000,
          installmentAmount: 4583,
          nextInstallmentDate: '2024-02-15',
          totalInstallments: 12,
          paidInstallments: 3,
          status: 'active'
        }
      ],
      recentPayments: [
        { id: '1', date: '2024-01-15', amount: 4583, type: 'installment', status: 'completed', description: 'Monthly loan installment' },
        { id: '2', date: '2024-01-10', amount: 2000, type: 'savings', status: 'completed', description: 'Monthly savings deposit' },
        { id: '3', date: '2024-01-05', amount: 500, type: 'fee', status: 'completed', description: 'Account maintenance fee' }
      ],
      savingsAccounts: [
        { id: '1', type: 'general', balance: 25000, interestRate: 6.5 },
        { id: '2', type: 'dps', balance: 15000, monthlyAmount: 2000, interestRate: 8.0 },
        { id: '3', type: 'fdr', balance: 50000, maturityDate: '2024-12-31', interestRate: 10.0 }
      ],
      quickActions: [
        { id: '1', label: 'Make Payment', icon: 'credit-card', href: '/payments', color: 'primary' },
        { id: '2', label: 'Apply for Loan', icon: 'file-text', href: '/loans/apply', color: 'success' },
        { id: '3', label: 'Loan Calculator', icon: 'calculator', href: '/calculator', color: 'info' },
        { id: '4', label: 'Contact Support', icon: 'phone', href: '/support', color: 'secondary' }
      ]
    };
  }

  private static getMockActivities(): ActivityItem[] {
    return [
      {
        id: '1',
        type: 'installment_collection',
        title: 'Installment Collected',
        description: 'Collected ৳4,583 from Fatima Begum',
        timestamp: '2024-01-15T14:30:00Z',
        user: 'Rahul Ahmed',
        amount: 4583,
        status: 'completed'
      },
      {
        id: '2',
        type: 'loan_application',
        title: 'New Loan Application',
        description: 'Mohammad Ali applied for ৳75,000 loan',
        timestamp: '2024-01-15T11:20:00Z',
        user: 'Mohammad Ali',
        amount: 75000,
        status: 'pending'
      },
      {
        id: '3',
        type: 'member_registration',
        title: 'New Member Registered',
        description: 'Rashida Khatun registered as new member',
        timestamp: '2024-01-15T09:45:00Z',
        user: 'Fatima Khan',
        status: 'completed'
      }
    ];
  }

  private static getMockUpcomingCollections(): InstallmentSummary[] {
    return [
      {
        id: '1',
        memberName: 'Fatima Begum',
        memberId: 'MEM001',
        amount: 4583,
        dueDate: '2024-02-15',
        status: 'pending',
        loanId: 'LOAN001',
        installmentNo: 4
      },
      {
        id: '2',
        memberName: 'Mohammad Ali',
        memberId: 'MEM002',
        amount: 6250,
        dueDate: '2024-02-16',
        status: 'pending',
        loanId: 'LOAN002',
        installmentNo: 2
      },
      {
        id: '3',
        memberName: 'Rashida Khatun',
        memberId: 'MEM003',
        amount: 3750,
        dueDate: '2024-02-10',
        status: 'overdue',
        loanId: 'LOAN003',
        installmentNo: 5
      }
    ];
  }
}

export default DashboardService;
