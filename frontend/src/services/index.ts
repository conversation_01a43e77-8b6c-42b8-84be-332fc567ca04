// API Client
export { apiClient } from '../utils/api';

// Authentication Services
export { AuthService, authService } from './authService';

// User Management Services
export { UserService, userService } from './userService';
export type {
  CreateUserData,
  UpdateUserData,
  UserListQuery,
  UserStats,
  BulkImportResult,
} from './userService';

// Branch Management Services
export { BranchService, branchService } from './branchService';
export type {
  CreateBranchData,
  UpdateBranchData,
  BranchListQuery,
  BranchStats,
  BranchMembersQuery,
  BranchFieldOfficersQuery,
} from './branchService';

// Member Management Services
export { default as MemberService } from './memberService';
export type {
  CreateMemberData,
  UpdateMemberData,
  MemberListQuery,
  MemberSearchQuery,
  MemberSearchResult,
} from '../types';

// Loan Management Services
export { LoanService, loanService } from './loanService';
export type {
  LoanApplication,
  CreateLoanApplicationData,
  UpdateLoanApplicationData,
  LoanApplicationListQuery,
  Loan,
  LoanListQuery,
  LoanInstallment,
  ApprovalData,
  RejectionData,
} from './loanService';

// Installment Services
export { InstallmentService, installmentService } from './installmentService';
export type {
  Installment,
  InstallmentListQuery,
  CollectInstallmentData,
  BulkCollectData,
  InstallmentStats,
  InstallmentSchedule,
} from './installmentService';

// Financial Services
export { FinancialService, financialService } from './financialService';
export type {
  Transaction,
  CreateTransactionData,
  TransactionListQuery,
  FinancialReport,
  BranchSummary,
  CashFlowData,
  ProfitLossStatement,
  BalanceSheet,
} from './financialService';

// Dashboard Services
export { default as DashboardService } from './dashboardService';

// Upload Services
export { UploadService, uploadService } from './uploadService';
export type {
  UploadProgress,
  UploadResult,
  BulkUploadResult,
  UploadOptions,
} from './uploadService';

// Service Factory for creating service instances
export class ServiceFactory {
  private static instances: Map<string, any> = new Map();

  static getAuthService(): AuthService {
    if (!this.instances.has('auth')) {
      this.instances.set('auth', authService);
    }
    return this.instances.get('auth');
  }

  static getUserService(): UserService {
    if (!this.instances.has('user')) {
      this.instances.set('user', userService);
    }
    return this.instances.get('user');
  }

  static getBranchService(): BranchService {
    if (!this.instances.has('branch')) {
      this.instances.set('branch', branchService);
    }
    return this.instances.get('branch');
  }

  static getMemberService(): typeof MemberService {
    return MemberService;
  }

  static getLoanService(): LoanService {
    if (!this.instances.has('loan')) {
      this.instances.set('loan', loanService);
    }
    return this.instances.get('loan');
  }

  static getInstallmentService(): InstallmentService {
    if (!this.instances.has('installment')) {
      this.instances.set('installment', installmentService);
    }
    return this.instances.get('installment');
  }

  static getFinancialService(): FinancialService {
    if (!this.instances.has('financial')) {
      this.instances.set('financial', financialService);
    }
    return this.instances.get('financial');
  }

  static getDashboardService(): typeof DashboardService {
    return DashboardService;
  }

  static getUploadService(): typeof UploadService {
    return UploadService;
  }

  static clearCache(): void {
    apiClient.clearCache();
  }

  static isOnline(): boolean {
    return apiClient.isOnline();
  }

  static async healthCheck(): Promise<boolean> {
    return apiClient.healthCheck();
  }
}

// Utility functions for common operations
export const ServiceUtils = {
  // Format currency
  formatCurrency: (amount: number, currency: string = 'BDT'): string => {
    return new Intl.NumberFormat('bn-BD', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(amount);
  },

  // Format date
  formatDate: (date: string | Date, locale: string = 'bn-BD'): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString(locale);
  },

  // Format date and time
  formatDateTime: (date: string | Date, locale: string = 'bn-BD'): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleString(locale);
  },

  // Calculate age from date of birth
  calculateAge: (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  },

  // Calculate loan EMI
  calculateEMI: (principal: number, rate: number, tenure: number): number => {
    const monthlyRate = rate / (12 * 100);
    const emi = (principal * monthlyRate * Math.pow(1 + monthlyRate, tenure)) / 
                (Math.pow(1 + monthlyRate, tenure) - 1);
    return Math.round(emi * 100) / 100;
  },

  // Validate Bangladesh phone number
  validateBDPhoneNumber: (phone: string): boolean => {
    const bdPhoneRegex = /^(\+88)?01[3-9]\d{8}$/;
    return bdPhoneRegex.test(phone);
  },

  // Validate Bangladesh NID number
  validateBDNID: (nid: string): boolean => {
    // Bangladesh NID can be 10, 13, or 17 digits
    const nidRegex = /^\d{10}$|^\d{13}$|^\d{17}$/;
    return nidRegex.test(nid);
  },

  // Generate member ID
  generateMemberID: (branchCode: string, sequence: number): string => {
    const year = new Date().getFullYear().toString().slice(-2);
    const paddedSequence = sequence.toString().padStart(4, '0');
    return `${branchCode}${year}${paddedSequence}`;
  },

  // Calculate collection efficiency
  calculateCollectionEfficiency: (collected: number, target: number): number => {
    if (target === 0) return 0;
    return Math.round((collected / target) * 100 * 100) / 100;
  },

  // Get status color
  getStatusColor: (status: string): string => {
    const statusColors: Record<string, string> = {
      active: 'green',
      inactive: 'red',
      pending: 'yellow',
      approved: 'green',
      rejected: 'red',
      completed: 'blue',
      overdue: 'red',
      paid: 'green',
      partial: 'orange',
    };
    return statusColors[status.toLowerCase()] || 'gray';
  },

  // Debounce function for search
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  // Retry function for failed operations
  retry: async <T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> => {
    let lastError: Error;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (i === maxRetries) {
          throw lastError;
        }
        
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
      }
    }
    
    throw lastError!;
  },
};

// Export default service factory
export default ServiceFactory;
