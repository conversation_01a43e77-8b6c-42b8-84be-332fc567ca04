import { apiClient } from '../utils/api';

export interface RealtimeEvent {
  id: string;
  type: string;
  data: any;
  timestamp: string;
  userId?: string;
  branchId?: string;
}

export interface NotificationEvent extends RealtimeEvent {
  type: 'notification';
  data: {
    title: string;
    message: string;
    severity: 'info' | 'success' | 'warning' | 'error';
    actionUrl?: string;
  };
}

export interface DataUpdateEvent extends RealtimeEvent {
  type: 'data_update';
  data: {
    entity: 'member' | 'loan' | 'installment' | 'transaction' | 'user';
    action: 'created' | 'updated' | 'deleted';
    entityId: string;
    changes?: Record<string, any>;
  };
}

export interface SystemEvent extends RealtimeEvent {
  type: 'system';
  data: {
    event: 'maintenance' | 'update' | 'alert';
    message: string;
    severity: 'info' | 'warning' | 'critical';
  };
}

export type EventHandler<T extends RealtimeEvent = RealtimeEvent> = (event: T) => void;

export class RealtimeService {
  private eventSource: EventSource | null = null;
  private eventHandlers: Map<string, Set<EventHandler>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnected = false;
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';
  }

  /**
   * Connect to the real-time event stream
   */
  async connect(): Promise<void> {
    if (this.eventSource) {
      this.disconnect();
    }

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token available');
      }

      const url = `${this.baseUrl}/events/stream?token=${encodeURIComponent(token)}`;
      this.eventSource = new EventSource(url);

      this.eventSource.onopen = () => {
        console.log('✅ Real-time connection established');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.emit('connection', { type: 'connection', data: { status: 'connected' } });
      };

      this.eventSource.onmessage = (event) => {
        try {
          const realtimeEvent: RealtimeEvent = JSON.parse(event.data);
          this.handleEvent(realtimeEvent);
        } catch (error) {
          console.error('Failed to parse real-time event:', error);
        }
      };

      this.eventSource.onerror = (error) => {
        console.error('Real-time connection error:', error);
        this.isConnected = false;
        this.emit('connection', { type: 'connection', data: { status: 'error', error } });
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      };

      // Set up specific event listeners
      this.setupEventListeners();

    } catch (error) {
      console.error('Failed to connect to real-time service:', error);
      throw error;
    }
  }

  /**
   * Disconnect from the real-time event stream
   */
  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.isConnected = false;
    this.emit('connection', { type: 'connection', data: { status: 'disconnected' } });
  }

  /**
   * Check if connected to real-time service
   */
  isConnectedToRealtime(): boolean {
    return this.isConnected && this.eventSource?.readyState === EventSource.OPEN;
  }

  /**
   * Subscribe to specific event types
   */
  on<T extends RealtimeEvent = RealtimeEvent>(eventType: string, handler: EventHandler<T>): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set());
    }
    this.eventHandlers.get(eventType)!.add(handler as EventHandler);
  }

  /**
   * Unsubscribe from specific event types
   */
  off<T extends RealtimeEvent = RealtimeEvent>(eventType: string, handler: EventHandler<T>): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.delete(handler as EventHandler);
      if (handlers.size === 0) {
        this.eventHandlers.delete(eventType);
      }
    }
  }

  /**
   * Subscribe to notifications
   */
  onNotification(handler: EventHandler<NotificationEvent>): void {
    this.on('notification', handler);
  }

  /**
   * Subscribe to data updates
   */
  onDataUpdate(handler: EventHandler<DataUpdateEvent>): void {
    this.on('data_update', handler);
  }

  /**
   * Subscribe to system events
   */
  onSystemEvent(handler: EventHandler<SystemEvent>): void {
    this.on('system', handler);
  }

  /**
   * Subscribe to connection status changes
   */
  onConnectionChange(handler: EventHandler): void {
    this.on('connection', handler);
  }

  /**
   * Send a real-time event (for testing or manual triggers)
   */
  async sendEvent(event: Omit<RealtimeEvent, 'id' | 'timestamp'>): Promise<void> {
    try {
      await apiClient.post('/events/send', event);
    } catch (error) {
      console.error('Failed to send real-time event:', error);
      throw error;
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    connected: boolean;
    readyState?: number;
    reconnectAttempts: number;
  } {
    return {
      connected: this.isConnected,
      readyState: this.eventSource?.readyState,
      reconnectAttempts: this.reconnectAttempts,
    };
  }

  private setupEventListeners(): void {
    if (!this.eventSource) return;

    // Listen for specific event types
    this.eventSource.addEventListener('notification', (event) => {
      const notificationEvent: NotificationEvent = JSON.parse(event.data);
      this.handleEvent(notificationEvent);
    });

    this.eventSource.addEventListener('data_update', (event) => {
      const dataUpdateEvent: DataUpdateEvent = JSON.parse(event.data);
      this.handleEvent(dataUpdateEvent);
      this.handleDataUpdate(dataUpdateEvent);
    });

    this.eventSource.addEventListener('system', (event) => {
      const systemEvent: SystemEvent = JSON.parse(event.data);
      this.handleEvent(systemEvent);
    });
  }

  private handleEvent(event: RealtimeEvent): void {
    // Emit to specific event type handlers
    this.emit(event.type, event);
    
    // Emit to general event handlers
    this.emit('*', event);

    // Log event in development
    if (import.meta.env.DEV) {
      console.log('📡 Real-time event received:', event);
    }
  }

  private handleDataUpdate(event: DataUpdateEvent): void {
    // Clear relevant caches when data is updated
    const { entity, action, entityId } = event.data;
    
    switch (entity) {
      case 'member':
        apiClient.removeCacheEntry('/members');
        if (entityId) {
          apiClient.removeCacheEntry(`/members/${entityId}`);
        }
        break;
      case 'loan':
        apiClient.removeCacheEntry('/loan-applications');
        apiClient.removeCacheEntry('/loans');
        if (entityId) {
          apiClient.removeCacheEntry(`/loans/${entityId}`);
        }
        break;
      case 'installment':
        apiClient.removeCacheEntry('/installments');
        apiClient.removeCacheEntry('/installments/pending');
        apiClient.removeCacheEntry('/installments/overdue');
        break;
      case 'transaction':
        apiClient.removeCacheEntry('/transactions');
        break;
      case 'user':
        apiClient.removeCacheEntry('/users');
        if (entityId) {
          apiClient.removeCacheEntry(`/users/${entityId}`);
        }
        break;
    }
  }

  private emit(eventType: string, event: any): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event);
        } catch (error) {
          console.error(`Error in event handler for ${eventType}:`, error);
        }
      });
    }
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.connect().catch(error => {
          console.error('Reconnect attempt failed:', error);
        });
      }
    }, delay);
  }
}

// Create singleton instance
export const realtimeService = new RealtimeService();

// Auto-connect when user is authenticated
const token = localStorage.getItem('token');
if (token && navigator.onLine) {
  realtimeService.connect().catch(error => {
    console.warn('Failed to establish real-time connection:', error);
  });
}

// Reconnect when coming back online
window.addEventListener('online', () => {
  if (!realtimeService.isConnectedToRealtime()) {
    realtimeService.connect().catch(error => {
      console.warn('Failed to reconnect to real-time service:', error);
    });
  }
});

// Disconnect when going offline
window.addEventListener('offline', () => {
  realtimeService.disconnect();
});

export default realtimeService;
