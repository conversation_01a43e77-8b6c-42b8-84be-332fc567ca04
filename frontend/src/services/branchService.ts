import { apiClient } from '../utils/api';
import { API_ENDPOINTS } from '../utils/constants';
import { Branch, User, Member, PaginatedResponse, PaginationParams } from '../types';

export interface CreateBranchData {
  name: string;
  address: string;
  managerId?: string;
}

export interface UpdateBranchData {
  name?: string;
  address?: string;
  managerId?: string;
  isActive?: boolean;
}

export interface BranchListQuery extends PaginationParams {
  search?: string;
  isActive?: boolean;
  managerId?: string;
}

export interface BranchStats {
  totalMembers: number;
  activeMembers: number;
  totalLoans: number;
  activeLoans: number;
  totalCollections: number;
  monthlyTarget: number;
  achievementPercentage: number;
  fieldOfficersCount: number;
}

export interface BranchMembersQuery extends PaginationParams {
  search?: string;
  isActive?: boolean;
}

export interface BranchFieldOfficersQuery extends PaginationParams {
  search?: string;
  isActive?: boolean;
}

export class BranchService {
  async getBranches(query: BranchListQuery = {}): Promise<PaginatedResponse<Branch>> {
    const response = await apiClient.get<PaginatedResponse<Branch>>(
      API_ENDPOINTS.BRANCHES.LIST,
      {
        params: query,
        cache: { ttl: 60000 }, // Cache for 1 minute
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch branches');
  }

  async getBranchById(id: string): Promise<Branch> {
    const response = await apiClient.get<Branch>(
      API_ENDPOINTS.BRANCHES.GET(id),
      {
        cache: { ttl: 60000 }, // Cache for 1 minute
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch branch');
  }

  async createBranch(data: CreateBranchData): Promise<Branch> {
    const response = await apiClient.post<Branch>(
      API_ENDPOINTS.BRANCHES.CREATE,
      data
    );

    if (response.success && response.data) {
      // Clear branches cache after creating new branch
      apiClient.removeCacheEntry(API_ENDPOINTS.BRANCHES.LIST);
      return response.data;
    }

    throw new Error(response.error || 'Failed to create branch');
  }

  async updateBranch(id: string, data: UpdateBranchData): Promise<Branch> {
    const response = await apiClient.put<Branch>(
      API_ENDPOINTS.BRANCHES.UPDATE(id),
      data
    );

    if (response.success && response.data) {
      // Clear relevant caches
      apiClient.removeCacheEntry(API_ENDPOINTS.BRANCHES.LIST);
      apiClient.removeCacheEntry(API_ENDPOINTS.BRANCHES.GET(id));
      return response.data;
    }

    throw new Error(response.error || 'Failed to update branch');
  }

  async deleteBranch(id: string): Promise<void> {
    const response = await apiClient.delete(API_ENDPOINTS.BRANCHES.DELETE(id));

    if (!response.success) {
      throw new Error(response.error || 'Failed to delete branch');
    }

    // Clear relevant caches
    apiClient.removeCacheEntry(API_ENDPOINTS.BRANCHES.LIST);
    apiClient.removeCacheEntry(API_ENDPOINTS.BRANCHES.GET(id));
  }

  async getBranchStats(id: string): Promise<BranchStats> {
    const response = await apiClient.get<BranchStats>(
      API_ENDPOINTS.BRANCHES.STATS(id),
      {
        cache: { ttl: 30000 }, // Cache for 30 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch branch statistics');
  }

  async getBranchMembers(
    branchId: string,
    query: BranchMembersQuery = {}
  ): Promise<PaginatedResponse<Member>> {
    const response = await apiClient.get<PaginatedResponse<Member>>(
      API_ENDPOINTS.BRANCHES.MEMBERS(branchId),
      {
        params: query,
        cache: { ttl: 30000 }, // Cache for 30 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch branch members');
  }

  async getBranchFieldOfficers(
    branchId: string,
    query: BranchFieldOfficersQuery = {}
  ): Promise<PaginatedResponse<User>> {
    const response = await apiClient.get<PaginatedResponse<User>>(
      API_ENDPOINTS.BRANCHES.FIELD_OFFICERS(branchId),
      {
        params: query,
        cache: { ttl: 60000 }, // Cache for 1 minute
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch branch field officers');
  }

  // Utility methods
  async searchBranches(query: string): Promise<Branch[]> {
    const result = await this.getBranches({ search: query, limit: 20 });
    return result.data;
  }

  async getActiveBranches(): Promise<Branch[]> {
    const result = await this.getBranches({ isActive: true, limit: 100 });
    return result.data;
  }

  async getBranchesByManager(managerId: string): Promise<Branch[]> {
    const result = await this.getBranches({ managerId, limit: 100 });
    return result.data;
  }

  async getBranchOptions(): Promise<Array<{ value: string; label: string }>> {
    const branches = await this.getActiveBranches();
    return branches.map(branch => ({
      value: branch.id,
      label: branch.name,
    }));
  }

  // Dashboard specific methods
  async getBranchDashboardData(branchId: string): Promise<{
    stats: BranchStats;
    recentMembers: Member[];
    fieldOfficers: User[];
  }> {
    const [stats, membersResult, fieldOfficersResult] = await Promise.all([
      this.getBranchStats(branchId),
      this.getBranchMembers(branchId, { limit: 5, sortBy: 'createdAt', sortOrder: 'desc' }),
      this.getBranchFieldOfficers(branchId, { limit: 10, isActive: true }),
    ]);

    return {
      stats,
      recentMembers: membersResult.data,
      fieldOfficers: fieldOfficersResult.data,
    };
  }

  // Performance tracking
  async getBranchPerformance(branchId: string, period: 'week' | 'month' | 'quarter' | 'year' = 'month'): Promise<{
    collections: number;
    disbursements: number;
    newMembers: number;
    targetAchievement: number;
  }> {
    const response = await apiClient.get(
      `${API_ENDPOINTS.BRANCHES.GET(branchId)}/performance`,
      {
        params: { period },
        cache: { ttl: 300000 }, // Cache for 5 minutes
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch branch performance');
  }
}

export const branchService = new BranchService();
export default branchService;
