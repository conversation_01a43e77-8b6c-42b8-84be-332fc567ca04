import { apiClient } from '../utils/api';
import { API_ENDPOINTS } from '../utils/constants';
import { PaginatedResponse, PaginationParams } from '../types';

export interface Installment {
  id: string;
  loanId: string;
  memberId: string;
  memberName: string;
  memberMemberId: string;
  installmentNo: number;
  amount: number;
  dueDate: string;
  paidDate?: string;
  paidAmount?: number;
  status: 'pending' | 'paid' | 'overdue' | 'partial';
  lateFee?: number;
  collectedBy?: string;
  collectorName?: string;
  paymentMethod?: 'cash' | 'bank_transfer' | 'mobile_banking';
  transactionRef?: string;
  notes?: string;
  branchId: string;
  branchName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface InstallmentListQuery extends PaginationParams {
  search?: string;
  status?: Installment['status'];
  branchId?: string;
  memberId?: string;
  loanId?: string;
  collectedBy?: string;
  dateFrom?: string;
  dateTo?: string;
  dueDateFrom?: string;
  dueDateTo?: string;
}

export interface CollectInstallmentData {
  amount: number;
  paymentMethod: 'cash' | 'bank_transfer' | 'mobile_banking';
  transactionRef?: string;
  notes?: string;
  collectionDate?: string;
}

export interface BulkCollectData {
  installmentIds: string[];
  paymentMethod: 'cash' | 'bank_transfer' | 'mobile_banking';
  transactionRef?: string;
  notes?: string;
  collectionDate?: string;
}

export interface InstallmentStats {
  totalPending: number;
  totalOverdue: number;
  totalCollected: number;
  pendingAmount: number;
  overdueAmount: number;
  collectedAmount: number;
  collectionTarget: number;
  achievementPercentage: number;
}

export interface InstallmentSchedule {
  loanId: string;
  memberName: string;
  memberMemberId: string;
  loanAmount: number;
  installments: Array<{
    installmentNo: number;
    amount: number;
    dueDate: string;
    status: 'pending' | 'paid' | 'overdue';
  }>;
}

export class InstallmentService {
  async getInstallments(query: InstallmentListQuery = {}): Promise<PaginatedResponse<Installment>> {
    const response = await apiClient.get<PaginatedResponse<Installment>>(
      API_ENDPOINTS.INSTALLMENTS.LIST,
      {
        params: query,
        cache: { ttl: 15000 }, // Cache for 15 seconds (shorter for real-time data)
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch installments');
  }

  async getPendingInstallments(query: Omit<InstallmentListQuery, 'status'> = {}): Promise<PaginatedResponse<Installment>> {
    const response = await apiClient.get<PaginatedResponse<Installment>>(
      API_ENDPOINTS.INSTALLMENTS.PENDING,
      {
        params: query,
        cache: { ttl: 15000 }, // Cache for 15 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch pending installments');
  }

  async getOverdueInstallments(query: Omit<InstallmentListQuery, 'status'> = {}): Promise<PaginatedResponse<Installment>> {
    const response = await apiClient.get<PaginatedResponse<Installment>>(
      API_ENDPOINTS.INSTALLMENTS.OVERDUE,
      {
        params: query,
        cache: { ttl: 15000 }, // Cache for 15 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch overdue installments');
  }

  async collectInstallment(installmentId: string, data: CollectInstallmentData): Promise<Installment> {
    const response = await apiClient.post<Installment>(
      API_ENDPOINTS.INSTALLMENTS.COLLECT(installmentId),
      data
    );

    if (response.success && response.data) {
      // Clear relevant caches
      apiClient.removeCacheEntry(API_ENDPOINTS.INSTALLMENTS.LIST);
      apiClient.removeCacheEntry(API_ENDPOINTS.INSTALLMENTS.PENDING);
      apiClient.removeCacheEntry(API_ENDPOINTS.INSTALLMENTS.OVERDUE);
      return response.data;
    }

    throw new Error(response.error || 'Failed to collect installment');
  }

  async bulkCollectInstallments(data: BulkCollectData): Promise<{
    successful: number;
    failed: number;
    results: Array<{
      installmentId: string;
      success: boolean;
      error?: string;
    }>;
  }> {
    const response = await apiClient.post(
      API_ENDPOINTS.INSTALLMENTS.BULK_COLLECT,
      data
    );

    if (response.success && response.data) {
      // Clear relevant caches
      apiClient.removeCacheEntry(API_ENDPOINTS.INSTALLMENTS.LIST);
      apiClient.removeCacheEntry(API_ENDPOINTS.INSTALLMENTS.PENDING);
      apiClient.removeCacheEntry(API_ENDPOINTS.INSTALLMENTS.OVERDUE);
      return response.data;
    }

    throw new Error(response.error || 'Failed to bulk collect installments');
  }

  async getInstallmentStats(branchId?: string, dateFrom?: string, dateTo?: string): Promise<InstallmentStats> {
    const params: any = {};
    if (branchId) params.branchId = branchId;
    if (dateFrom) params.dateFrom = dateFrom;
    if (dateTo) params.dateTo = dateTo;

    const response = await apiClient.get<InstallmentStats>(
      API_ENDPOINTS.INSTALLMENTS.STATS,
      {
        params,
        cache: { ttl: 30000 }, // Cache for 30 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch installment statistics');
  }

  async getInstallmentSchedule(loanId: string): Promise<InstallmentSchedule> {
    const response = await apiClient.get<InstallmentSchedule>(
      API_ENDPOINTS.INSTALLMENTS.SCHEDULE(loanId),
      {
        cache: { ttl: 60000 }, // Cache for 1 minute
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch installment schedule');
  }

  async exportInstallments(query: InstallmentListQuery = {}): Promise<Blob> {
    const response = await apiClient.get(
      API_ENDPOINTS.INSTALLMENTS.EXPORT,
      {
        params: query,
        responseType: 'blob',
      }
    );

    if (response instanceof Blob) {
      return response;
    }

    throw new Error('Failed to export installments');
  }

  // Utility methods
  async getTodaysPendingInstallments(branchId?: string): Promise<Installment[]> {
    const today = new Date().toISOString().split('T')[0];
    const query: InstallmentListQuery = {
      dueDateFrom: today,
      dueDateTo: today,
      status: 'pending',
      limit: 100,
    };
    if (branchId) query.branchId = branchId;

    const result = await this.getPendingInstallments(query);
    return result.data;
  }

  async getUpcomingInstallments(branchId?: string, days: number = 7): Promise<Installment[]> {
    const today = new Date();
    const futureDate = new Date(today.getTime() + days * 24 * 60 * 60 * 1000);
    
    const query: InstallmentListQuery = {
      dueDateFrom: today.toISOString().split('T')[0],
      dueDateTo: futureDate.toISOString().split('T')[0],
      status: 'pending',
      limit: 100,
    };
    if (branchId) query.branchId = branchId;

    const result = await this.getPendingInstallments(query);
    return result.data;
  }

  async getMemberInstallments(memberId: string): Promise<Installment[]> {
    const result = await this.getInstallments({ memberId, limit: 100 });
    return result.data;
  }

  async getFieldOfficerInstallments(fieldOfficerId: string): Promise<Installment[]> {
    const result = await this.getInstallments({ collectedBy: fieldOfficerId, limit: 100 });
    return result.data;
  }

  async getDailyCollectionReport(branchId?: string, date?: string): Promise<{
    totalCollected: number;
    totalAmount: number;
    installments: Installment[];
  }> {
    const targetDate = date || new Date().toISOString().split('T')[0];
    const query: InstallmentListQuery = {
      status: 'paid',
      dateFrom: targetDate,
      dateTo: targetDate,
      limit: 1000,
    };
    if (branchId) query.branchId = branchId;

    const result = await this.getInstallments(query);
    const totalAmount = result.data.reduce((sum, installment) => sum + (installment.paidAmount || 0), 0);

    return {
      totalCollected: result.data.length,
      totalAmount,
      installments: result.data,
    };
  }
}

export const installmentService = new InstallmentService();
