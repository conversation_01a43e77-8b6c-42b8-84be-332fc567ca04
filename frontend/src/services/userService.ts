import { apiClient } from '../utils/api';
import { API_ENDPOINTS } from '../utils/constants';
import { User, UserRole, PaginatedResponse, PaginationParams, ApiResponse } from '../types';

export interface CreateUserData {
  name: string;
  email: string;
  password: string;
  role: UserRole;
  memberId?: string;
  branchId?: string;
}

export interface UpdateUserData {
  name?: string;
  email?: string;
  role?: UserRole;
  memberId?: string;
  branchId?: string;
  isActive?: boolean;
}

export interface UserListQuery extends PaginationParams {
  search?: string;
  role?: UserRole;
  branchId?: string;
  isActive?: boolean;
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  usersByRole: Record<UserRole, number>;
  recentRegistrations: number;
}

export interface BulkImportResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  errors: Array<{
    row: number;
    error: string;
  }>;
}

export class UserService {
  async getUsers(query: UserListQuery = {}): Promise<PaginatedResponse<User>> {
    const response = await apiClient.get<PaginatedResponse<User>>(
      API_ENDPOINTS.USERS.LIST,
      {
        params: query,
        cache: { ttl: 30000 }, // Cache for 30 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch users');
  }

  async getUserById(id: string): Promise<User> {
    const response = await apiClient.get<User>(
      API_ENDPOINTS.USERS.GET(id),
      {
        cache: { ttl: 60000 }, // Cache for 1 minute
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch user');
  }

  async createUser(data: CreateUserData): Promise<User> {
    const response = await apiClient.post<User>(
      API_ENDPOINTS.USERS.CREATE,
      data
    );

    if (response.success && response.data) {
      // Clear users cache after creating new user
      apiClient.removeCacheEntry(API_ENDPOINTS.USERS.LIST);
      return response.data;
    }

    throw new Error(response.error || 'Failed to create user');
  }

  async updateUser(id: string, data: UpdateUserData): Promise<User> {
    const response = await apiClient.put<User>(
      API_ENDPOINTS.USERS.UPDATE(id),
      data
    );

    if (response.success && response.data) {
      // Clear relevant caches
      apiClient.removeCacheEntry(API_ENDPOINTS.USERS.LIST);
      apiClient.removeCacheEntry(API_ENDPOINTS.USERS.GET(id));
      return response.data;
    }

    throw new Error(response.error || 'Failed to update user');
  }

  async deleteUser(id: string): Promise<void> {
    const response = await apiClient.delete(API_ENDPOINTS.USERS.DELETE(id));

    if (!response.success) {
      throw new Error(response.error || 'Failed to delete user');
    }

    // Clear relevant caches
    apiClient.removeCacheEntry(API_ENDPOINTS.USERS.LIST);
    apiClient.removeCacheEntry(API_ENDPOINTS.USERS.GET(id));
  }

  async getUserStats(): Promise<UserStats> {
    const response = await apiClient.get<UserStats>(
      API_ENDPOINTS.USERS.STATS,
      {
        cache: { ttl: 60000 }, // Cache for 1 minute
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch user statistics');
  }

  async exportUsers(query: UserListQuery = {}): Promise<Blob> {
    const response = await apiClient.get(
      API_ENDPOINTS.USERS.EXPORT,
      {
        params: query,
        responseType: 'blob',
      }
    );

    if (response instanceof Blob) {
      return response;
    }

    throw new Error('Failed to export users');
  }

  async bulkImportUsers(file: File): Promise<BulkImportResult> {
    const response = await apiClient.uploadFile<BulkImportResult>(
      API_ENDPOINTS.USERS.BULK_IMPORT,
      file
    );

    if (response.success && response.data) {
      // Clear users cache after bulk import
      apiClient.removeCacheEntry(API_ENDPOINTS.USERS.LIST);
      return response.data;
    }

    throw new Error(response.error || 'Failed to import users');
  }

  async resetUserPassword(id: string, newPassword: string): Promise<void> {
    const response = await apiClient.put(
      API_ENDPOINTS.USERS.RESET_PASSWORD(id),
      { newPassword }
    );

    if (!response.success) {
      throw new Error(response.error || 'Failed to reset user password');
    }
  }

  async toggleUserStatus(id: string): Promise<User> {
    const response = await apiClient.put<User>(
      API_ENDPOINTS.USERS.TOGGLE_STATUS(id)
    );

    if (response.success && response.data) {
      // Clear relevant caches
      apiClient.removeCacheEntry(API_ENDPOINTS.USERS.LIST);
      apiClient.removeCacheEntry(API_ENDPOINTS.USERS.GET(id));
      return response.data;
    }

    throw new Error(response.error || 'Failed to toggle user status');
  }

  // Utility methods
  async searchUsers(query: string, filters?: Partial<UserListQuery>): Promise<User[]> {
    const searchQuery: UserListQuery = {
      search: query,
      limit: 20,
      ...filters,
    };

    const result = await this.getUsers(searchQuery);
    return result.data;
  }

  async getUsersByRole(role: UserRole): Promise<User[]> {
    const result = await this.getUsers({ role, limit: 100 });
    return result.data;
  }

  async getActiveUsers(): Promise<User[]> {
    const result = await this.getUsers({ isActive: true, limit: 100 });
    return result.data;
  }
}

export const userService = new UserService();
