import { apiClient } from '../utils/api';
import { API_ENDPOINTS } from '../utils/constants';
import { PaginatedResponse, PaginationParams } from '../types';

export interface Transaction {
  id: string;
  type: 'income' | 'expense' | 'loan_disbursement' | 'installment_collection' | 'fee_collection';
  category: string;
  amount: number;
  description: string;
  referenceId?: string; // Loan ID, Member ID, etc.
  referenceName?: string;
  paymentMethod: 'cash' | 'bank_transfer' | 'mobile_banking' | 'cheque';
  transactionRef?: string;
  branchId: string;
  branchName?: string;
  createdBy: string;
  creatorName?: string;
  approvedBy?: string;
  approverName?: string;
  status: 'pending' | 'approved' | 'rejected';
  transactionDate: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTransactionData {
  type: Transaction['type'];
  category: string;
  amount: number;
  description: string;
  referenceId?: string;
  paymentMethod: Transaction['paymentMethod'];
  transactionRef?: string;
  transactionDate?: string;
}

export interface TransactionListQuery extends PaginationParams {
  search?: string;
  type?: Transaction['type'];
  category?: string;
  branchId?: string;
  createdBy?: string;
  status?: Transaction['status'];
  dateFrom?: string;
  dateTo?: string;
  amountFrom?: number;
  amountTo?: number;
}

export interface FinancialReport {
  period: string;
  totalIncome: number;
  totalExpense: number;
  netProfit: number;
  loanDisbursements: number;
  installmentCollections: number;
  feeCollections: number;
  operatingExpenses: number;
  profitMargin: number;
}

export interface BranchSummary {
  branchId: string;
  branchName: string;
  totalIncome: number;
  totalExpense: number;
  netProfit: number;
  activeLoans: number;
  totalMembers: number;
  collectionRate: number;
  performance: number;
}

export interface CashFlowData {
  date: string;
  cashIn: number;
  cashOut: number;
  netCashFlow: number;
  runningBalance: number;
}

export interface ProfitLossStatement {
  period: string;
  revenue: {
    interestIncome: number;
    feeIncome: number;
    otherIncome: number;
    totalRevenue: number;
  };
  expenses: {
    operatingExpenses: number;
    administrativeExpenses: number;
    financialExpenses: number;
    totalExpenses: number;
  };
  netProfit: number;
  profitMargin: number;
}

export interface BalanceSheet {
  asOfDate: string;
  assets: {
    cash: number;
    loansOutstanding: number;
    otherAssets: number;
    totalAssets: number;
  };
  liabilities: {
    memberDeposits: number;
    otherLiabilities: number;
    totalLiabilities: number;
  };
  equity: {
    retainedEarnings: number;
    currentProfit: number;
    totalEquity: number;
  };
}

export class FinancialService {
  async getTransactions(query: TransactionListQuery = {}): Promise<PaginatedResponse<Transaction>> {
    const response = await apiClient.get<PaginatedResponse<Transaction>>(
      API_ENDPOINTS.FINANCIAL.TRANSACTIONS,
      {
        params: query,
        cache: { ttl: 30000 }, // Cache for 30 seconds
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch transactions');
  }

  async createTransaction(data: CreateTransactionData): Promise<Transaction> {
    const response = await apiClient.post<Transaction>(
      API_ENDPOINTS.FINANCIAL.CREATE_TRANSACTION,
      data
    );

    if (response.success && response.data) {
      // Clear transactions cache
      apiClient.removeCacheEntry(API_ENDPOINTS.FINANCIAL.TRANSACTIONS);
      return response.data;
    }

    throw new Error(response.error || 'Failed to create transaction');
  }

  async getFinancialReports(
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' = 'monthly',
    branchId?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<FinancialReport[]> {
    const params: any = { period };
    if (branchId) params.branchId = branchId;
    if (dateFrom) params.dateFrom = dateFrom;
    if (dateTo) params.dateTo = dateTo;

    const response = await apiClient.get<FinancialReport[]>(
      API_ENDPOINTS.FINANCIAL.REPORTS,
      {
        params,
        cache: { ttl: 300000 }, // Cache for 5 minutes
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch financial reports');
  }

  async getBranchSummary(
    period: 'monthly' | 'quarterly' | 'yearly' = 'monthly',
    dateFrom?: string,
    dateTo?: string
  ): Promise<BranchSummary[]> {
    const params: any = { period };
    if (dateFrom) params.dateFrom = dateFrom;
    if (dateTo) params.dateTo = dateTo;

    const response = await apiClient.get<BranchSummary[]>(
      API_ENDPOINTS.FINANCIAL.BRANCH_SUMMARY,
      {
        params,
        cache: { ttl: 300000 }, // Cache for 5 minutes
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch branch summary');
  }

  async getCashFlow(
    branchId?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<CashFlowData[]> {
    const params: any = {};
    if (branchId) params.branchId = branchId;
    if (dateFrom) params.dateFrom = dateFrom;
    if (dateTo) params.dateTo = dateTo;

    const response = await apiClient.get<CashFlowData[]>(
      API_ENDPOINTS.FINANCIAL.CASH_FLOW,
      {
        params,
        cache: { ttl: 300000 }, // Cache for 5 minutes
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch cash flow data');
  }

  async getProfitLossStatement(
    branchId?: string,
    dateFrom?: string,
    dateTo?: string
  ): Promise<ProfitLossStatement> {
    const params: any = {};
    if (branchId) params.branchId = branchId;
    if (dateFrom) params.dateFrom = dateFrom;
    if (dateTo) params.dateTo = dateTo;

    const response = await apiClient.get<ProfitLossStatement>(
      API_ENDPOINTS.FINANCIAL.PROFIT_LOSS,
      {
        params,
        cache: { ttl: 300000 }, // Cache for 5 minutes
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch profit & loss statement');
  }

  async getBalanceSheet(
    branchId?: string,
    asOfDate?: string
  ): Promise<BalanceSheet> {
    const params: any = {};
    if (branchId) params.branchId = branchId;
    if (asOfDate) params.asOfDate = asOfDate;

    const response = await apiClient.get<BalanceSheet>(
      API_ENDPOINTS.FINANCIAL.BALANCE_SHEET,
      {
        params,
        cache: { ttl: 300000 }, // Cache for 5 minutes
      }
    );

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error(response.error || 'Failed to fetch balance sheet');
  }

  async exportFinancialReport(
    reportType: 'transactions' | 'profit_loss' | 'balance_sheet' | 'cash_flow',
    query: any = {}
  ): Promise<Blob> {
    const response = await apiClient.get(
      API_ENDPOINTS.FINANCIAL.EXPORT_REPORT,
      {
        params: { reportType, ...query },
        responseType: 'blob',
      }
    );

    if (response instanceof Blob) {
      return response;
    }

    throw new Error('Failed to export financial report');
  }

  // Utility methods
  async getDailyTransactions(branchId?: string, date?: string): Promise<Transaction[]> {
    const targetDate = date || new Date().toISOString().split('T')[0];
    const query: TransactionListQuery = {
      dateFrom: targetDate,
      dateTo: targetDate,
      limit: 1000,
    };
    if (branchId) query.branchId = branchId;

    const result = await this.getTransactions(query);
    return result.data;
  }

  async getMonthlyReport(branchId?: string, year?: number, month?: number): Promise<FinancialReport> {
    const currentDate = new Date();
    const targetYear = year || currentDate.getFullYear();
    const targetMonth = month || currentDate.getMonth() + 1;
    
    const dateFrom = `${targetYear}-${targetMonth.toString().padStart(2, '0')}-01`;
    const lastDay = new Date(targetYear, targetMonth, 0).getDate();
    const dateTo = `${targetYear}-${targetMonth.toString().padStart(2, '0')}-${lastDay}`;

    const reports = await this.getFinancialReports('monthly', branchId, dateFrom, dateTo);
    return reports[0] || {
      period: `${targetYear}-${targetMonth.toString().padStart(2, '0')}`,
      totalIncome: 0,
      totalExpense: 0,
      netProfit: 0,
      loanDisbursements: 0,
      installmentCollections: 0,
      feeCollections: 0,
      operatingExpenses: 0,
      profitMargin: 0,
    };
  }

  async getIncomeTransactions(branchId?: string, dateFrom?: string, dateTo?: string): Promise<Transaction[]> {
    const query: TransactionListQuery = {
      type: 'income',
      limit: 1000,
    };
    if (branchId) query.branchId = branchId;
    if (dateFrom) query.dateFrom = dateFrom;
    if (dateTo) query.dateTo = dateTo;

    const result = await this.getTransactions(query);
    return result.data;
  }

  async getExpenseTransactions(branchId?: string, dateFrom?: string, dateTo?: string): Promise<Transaction[]> {
    const query: TransactionListQuery = {
      type: 'expense',
      limit: 1000,
    };
    if (branchId) query.branchId = branchId;
    if (dateFrom) query.dateFrom = dateFrom;
    if (dateTo) query.dateTo = dateTo;

    const result = await this.getTransactions(query);
    return result.data;
  }
}

export const financialService = new FinancialService();
