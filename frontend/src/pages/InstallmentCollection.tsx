import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Form,
  FormGroup,
  Input,
  Select,
  Textarea,
  Button,
  Alert,
  Loading
} from '../components/ui';
import { MemberSearch } from '../components/members';
import { CollectInstallmentData, MemberSearchResult } from '../types';
import { Installment } from '../services/installmentService';
import { useAuth } from '../contexts/AuthContext';
import { InstallmentService } from '../services/installmentService';
import { LoanService } from '../services/loanService';

export const InstallmentCollection: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedMember, setSelectedMember] = useState<MemberSearchResult | null>(null);
  const [pendingInstallments, setPendingInstallments] = useState<Installment[]>([]);
  const [selectedInstallment, setSelectedInstallment] = useState<Installment | null>(null);
  const [loadingInstallments, setLoadingInstallments] = useState(false);
  const [memberLoans, setMemberLoans] = useState<any[]>([]);
  const [collectionHistory, setCollectionHistory] = useState<any[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<CollectInstallmentData>();

  const fetchMemberData = async (memberId: string) => {
    try {
      setLoadingInstallments(true);

      // Fetch pending installments
      const installmentService = new InstallmentService();
      const installmentsResponse = await installmentService.getInstallments({
        memberId,
        status: 'pending'
      });
      setPendingInstallments(installmentsResponse.data);

      // Fetch member loans
      const loanService = new LoanService();
      const loansResponse = await loanService.getLoans({
        memberId,
        status: 'active'
      });
      setMemberLoans(loansResponse.data);

    } catch (err) {
      console.error('Error fetching member data:', err);
      setError('Failed to load member data.');
    } finally {
      setLoadingInstallments(false);
    }
  };

  const fetchCollectionHistory = async (memberId: string) => {
    try {
      setLoadingHistory(true);
      const installmentService = new InstallmentService();
      const historyResponse = await installmentService.getInstallments({
        memberId,
        status: 'paid',
        limit: 10
      });
      setCollectionHistory(historyResponse.data);
    } catch (err) {
      console.error('Error fetching collection history:', err);
    } finally {
      setLoadingHistory(false);
    }
  };

  const handleMemberSelect = (member: MemberSearchResult) => {
    setSelectedMember(member);
    setSelectedInstallment(null);
    setPendingInstallments([]);
    setMemberLoans([]);
    setCollectionHistory([]);
    setError(null);
    fetchMemberData(member.id);
    fetchCollectionHistory(member.id);
  };

  const handleInstallmentSelect = (installment: Installment) => {
    setSelectedInstallment(installment);
    setValue('amount', installment.amount);
  };

  const onSubmit = async (data: CollectInstallmentData) => {
    if (!selectedInstallment) {
      setError('Please select an installment to collect.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const installmentService = new InstallmentService();
      await installmentService.collectInstallment(selectedInstallment.id, {
        ...data,
        amount: Number(data.amount)
      });

      setSuccess(`Installment of ৳${data.amount} collected successfully!`);

      // Refresh installments
      if (selectedMember) {
        await fetchMemberData(selectedMember.id);
        await fetchCollectionHistory(selectedMember.id);
      }
      
      // Reset form
      reset();
      setSelectedInstallment(null);

      setTimeout(() => {
        navigate('/dashboard', {
          state: {
            message: `Installment collected successfully from ${selectedMember?.name}!`,
            type: 'success'
          }
        });
      }, 2000);
    } catch (err: any) {
      console.error('Error collecting installment:', err);
      setError(err.response?.data?.message || 'Failed to collect installment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'overdue':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Installment Collection
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Collect loan installments from members
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Dashboard
        </button>
      </div>

      {/* Success Alert */}
      {success && (
        <Alert 
          type="success" 
          title="Success" 
          description={success}
        />
      )}

      {/* Error Alert */}
      {error && (
        <Alert
          type="danger"
          title="Error"
          description={error}
          onDismiss={() => setError(null)}
          dismissible
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Member Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Select Member</CardTitle>
          </CardHeader>
          <CardContent>
            <MemberSearch
              value={selectedMember}
              onChange={handleMemberSelect}
              placeholder="Search for member by name or ID..."
              branchId={user?.branchId}
            />

            {selectedMember && (
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100">
                  {selectedMember.name}
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Member ID: {selectedMember.memberId}
                </p>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Phone: {selectedMember.phoneNumber}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Loan Details */}
        <Card>
          <CardHeader>
            <CardTitle>Loan Details</CardTitle>
          </CardHeader>
          <CardContent>
            {!selectedMember ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                Select a member to view loan details
              </p>
            ) : loadingInstallments ? (
              <div className="text-center py-8">
                <Loading size="sm" />
                <p className="text-gray-500 dark:text-gray-400 mt-2">Loading loans...</p>
              </div>
            ) : memberLoans.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                No active loans found
              </p>
            ) : (
              <div className="space-y-4">
                {memberLoans.map((loan) => (
                  <div key={loan.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        Loan #{loan.id.slice(-6)}
                      </h4>
                      <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 rounded-full text-xs font-medium">
                        {loan.status}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Loan Amount</p>
                        <p className="font-medium">৳{loan.loanAmount?.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Remaining</p>
                        <p className="font-medium">৳{loan.remainingAmount?.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Installment</p>
                        <p className="font-medium">৳{loan.installmentAmount?.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-600 dark:text-gray-400">Paid/Total</p>
                        <p className="font-medium">{loan.paidInstallments}/{loan.totalInstallments}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pending Installments */}
        <Card>
          <CardHeader>
            <CardTitle>Pending Installments</CardTitle>
          </CardHeader>
          <CardContent>
            {!selectedMember ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                Select a member to view pending installments
              </p>
            ) : loadingInstallments ? (
              <div className="text-center py-8">
                <Loading size="sm" />
                <p className="text-gray-500 dark:text-gray-400 mt-2">Loading installments...</p>
              </div>
            ) : pendingInstallments.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                No pending installments found
              </p>
            ) : (
              <div className="space-y-3">
                {pendingInstallments.map((installment) => (
                  <div
                    key={installment.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedInstallment?.id === installment.id
                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                    }`}
                    onClick={() => handleInstallmentSelect(installment)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="font-medium">৳{installment.amount.toLocaleString()}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Installment #{installment.installmentNo}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Due: {formatDate(installment.dueDate)}
                        </p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(installment.status)}`}>
                        {installment.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Collection Form */}
      {selectedInstallment && (
        <Card>
          <CardHeader>
            <CardTitle>Collect Installment</CardTitle>
          </CardHeader>
          <CardContent>
            <Form onSubmit={handleSubmit(onSubmit)} spacing="lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Collection Amount (৳)"
                  type="number"
                  step="0.01"
                  {...register('amount', { 
                    required: 'Amount is required',
                    min: { value: 0.01, message: 'Amount must be greater than 0' }
                  })}
                  error={errors.amount?.message}
                  required
                />

                <Select
                  label="Payment Method"
                  {...register('paymentMethod', { required: 'Payment method is required' })}
                  error={errors.paymentMethod?.message}
                  required
                  placeholder="Select payment method"
                  options={[
                    { value: 'cash', label: 'Cash' },
                    { value: 'bank_transfer', label: 'Bank Transfer' },
                    { value: 'mobile_banking', label: 'Mobile Banking' }
                  ]}
                />
              </div>

              <Input
                label="Transaction Reference (Optional)"
                {...register('transactionRef')}
                placeholder="Enter transaction reference number"
              />

              <Textarea
                label="Notes (Optional)"
                rows={3}
                {...register('notes')}
                placeholder="Add any additional notes..."
              />

              <div className="flex gap-4 pt-4">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={loading}
                  className="flex-1"
                >
                  {loading ? 'Collecting...' : 'Collect Installment'}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => setSelectedInstallment(null)}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>
      )}

      {/* Collection History */}
      {selectedMember && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>📋</span>
              Collection History
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loadingHistory ? (
              <div className="text-center py-8">
                <Loading size="sm" />
                <p className="text-gray-500 dark:text-gray-400 mt-2">Loading history...</p>
              </div>
            ) : collectionHistory.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                No collection history found
              </p>
            ) : (
              <div className="space-y-3">
                {collectionHistory.map((collection) => (
                  <div key={collection.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium text-gray-900 dark:text-white">
                            ৳{collection.amount.toLocaleString()}
                          </span>
                          <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 rounded-full text-xs font-medium">
                            Collected
                          </span>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
                          <div>
                            <p>Installment #{collection.installmentNo}</p>
                            <p>Due: {formatDate(collection.dueDate)}</p>
                          </div>
                          <div>
                            <p>Collected: {formatDate(collection.paidDate || collection.collectionDate)}</p>
                            <p>Method: {collection.paymentMethod || 'Cash'}</p>
                          </div>
                        </div>
                        {collection.notes && (
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                            Note: {collection.notes}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {collectionHistory.length >= 10 && (
                  <div className="text-center pt-4">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => {
                        // Navigate to full collection history page
                        navigate(`/collections/history/${selectedMember.id}`);
                      }}
                    >
                      View All Collections
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default InstallmentCollection;
