import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Form,
  Input,
  Select,
  Button,
  Alert
} from '../components/ui';
import { LoanCalculationData, LoanCalculationResult, RepaymentMethod } from '../types';
import { LoanService } from '../services/loanService';

export const LoanCalculator: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [calculationResult, setCalculationResult] = useState<LoanCalculationResult | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<LoanCalculationData>({
    defaultValues: {
      repaymentMethod: RepaymentMethod.MONTHLY,
      interestRate: 10,
      advancePayment: 0
    }
  });

  // Handle pre-filled data from navigation state
  useEffect(() => {
    if (location.state) {
      const { loanAmount, advancePayment } = location.state as any;
      if (loanAmount) setValue('loanAmount', loanAmount);
      if (advancePayment) setValue('advancePayment', advancePayment);
    }
  }, [location.state, setValue]);

  const onSubmit = async (data: LoanCalculationData) => {
    try {
      setLoading(true);
      setError(null);

      const calculationData = {
        ...data,
        loanAmount: Number(data.loanAmount),
        repaymentDuration: Number(data.repaymentDuration),
        repaymentMethod: data.repaymentMethod as RepaymentMethod,
        advancePayment: Number(data.advancePayment || 0),
        interestRate: Number(data.interestRate || 10)
      };

      const loanService = new LoanService();
      const result = await loanService.calculateLoan(calculationData);
      setCalculationResult(result);
    } catch (err: any) {
      console.error('Error calculating loan:', err);
      setError(err.response?.data?.message || 'Failed to calculate loan. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  const formatCurrency = (amount: number) => {
    return `৳${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Loan Calculator
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Calculate loan installments and repayment schedule
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Dashboard
        </button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert
          type="danger"
          title="Error"
          description={error}
          onDismiss={() => setError(null)}
          dismissible
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Calculator Form */}
        <Card>
          <CardHeader>
            <CardTitle>Loan Details</CardTitle>
          </CardHeader>
          <CardContent>
            <Form onSubmit={handleSubmit(onSubmit)} spacing="lg">
              <Input
                label="Loan Amount (৳)"
                type="number"
                step="100"
                min="1000"
                {...register('loanAmount', { 
                  required: 'Loan amount is required',
                  min: { value: 1000, message: 'Minimum loan amount is ৳1,000' },
                  max: { value: 10000000, message: 'Maximum loan amount is ৳1,00,00,000' }
                })}
                error={errors.loanAmount?.message}
                required
              />

              <Input
                label="Repayment Duration (Months)"
                type="number"
                min="1"
                max="60"
                {...register('repaymentDuration', { 
                  required: 'Repayment duration is required',
                  min: { value: 1, message: 'Minimum duration is 1 month' },
                  max: { value: 60, message: 'Maximum duration is 60 months' }
                })}
                error={errors.repaymentDuration?.message}
                required
              />

              <Select
                label="Repayment Method"
                {...register('repaymentMethod', { required: 'Repayment method is required' })}
                error={errors.repaymentMethod?.message}
                required
                placeholder="Select repayment method"
                options={[
                  { value: 'WEEKLY', label: 'Weekly' },
                  { value: 'MONTHLY', label: 'Monthly' },
                  { value: 'QUARTERLY', label: 'Quarterly' }
                ]}
              />

              <Input
                label="Interest Rate (%)"
                type="number"
                step="0.1"
                min="0"
                max="50"
                {...register('interestRate')}
                error={errors.interestRate?.message}
              />

              <Input
                label="Advance Payment (৳)"
                type="number"
                step="100"
                min="0"
                {...register('advancePayment')}
                error={errors.advancePayment?.message}
              />

              <Button
                type="submit"
                variant="primary"
                disabled={loading}
                fullWidth
              >
                {loading ? 'Calculating...' : 'Calculate Loan'}
              </Button>
            </Form>
          </CardContent>
        </Card>

        {/* Calculation Results */}
        <Card>
          <CardHeader>
            <CardTitle>Calculation Results</CardTitle>
          </CardHeader>
          <CardContent>
            {!calculationResult ? (
              <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                <div className="text-4xl mb-4">🧮</div>
                <p>Enter loan details and click "Calculate Loan" to see results</p>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Formula Explanation */}
                <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border-l-4 border-blue-500">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                    📐 Calculation Formula
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <strong>Total Repayment - Advance Payment = Installment Amounts</strong>
                  </p>
                  <div className="text-xs text-gray-500 dark:text-gray-500 space-y-1">
                    <p>• Total Repayment = Loan Amount + Interest</p>
                    <p>• Remaining Amount = Total Repayment - Advance Payment</p>
                    <p>• Installment Amount = Remaining Amount ÷ Number of Installments</p>
                  </div>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p className="text-sm text-blue-600 dark:text-blue-400">Loan Amount</p>
                    <p className="text-xl font-bold text-blue-900 dark:text-blue-100">
                      {formatCurrency(calculationResult.loanAmount)}
                    </p>
                  </div>
                  <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <p className="text-sm text-orange-600 dark:text-orange-400">Total Repayment</p>
                    <p className="text-xl font-bold text-orange-900 dark:text-orange-100">
                      {formatCurrency(calculationResult.totalRepaymentAmount)}
                    </p>
                  </div>
                  <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                    <p className="text-sm text-yellow-600 dark:text-yellow-400">Advance Payment</p>
                    <p className="text-xl font-bold text-yellow-900 dark:text-yellow-100">
                      {formatCurrency(calculationResult.advancePayment)}
                    </p>
                  </div>
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <p className="text-sm text-green-600 dark:text-green-400">Remaining Amount</p>
                    <p className="text-xl font-bold text-green-900 dark:text-green-100">
                      {formatCurrency(calculationResult.totalRepaymentAmount - calculationResult.advancePayment)}
                    </p>
                  </div>
                </div>

                {/* Installment Breakdown */}
                <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border">
                  <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-3">
                    💰 Installment Breakdown
                  </h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-purple-600 dark:text-purple-400">Number of Installments</p>
                      <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                        {calculationResult.installmentCount}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-purple-600 dark:text-purple-400">Installment Amount per Period</p>
                      <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">
                        {formatCurrency(calculationResult.installmentAmount)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Detailed Information */}
                <div className="space-y-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Advance Payment:</span>
                    <span className="font-medium">{formatCurrency(calculationResult.advancePayment)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Interest Amount:</span>
                    <span className="font-medium">{formatCurrency(calculationResult.interestAmount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Number of Installments:</span>
                    <span className="font-medium">{calculationResult.installmentCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">First Installment Date:</span>
                    <span className="font-medium">{formatDate(calculationResult.firstInstallmentDate)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Last Installment Date:</span>
                    <span className="font-medium">{formatDate(calculationResult.lastInstallmentDate)}</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                  <Button
                    variant="primary"
                    onClick={() => navigate('/loans/apply', { 
                      state: { calculationResult } 
                    })}
                    fullWidth
                  >
                    Apply for This Loan
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LoanCalculator;
