import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Form,
  Input,
  Select,
  Textarea,
  Button,
  Alert,
  Loading
} from '../components/ui';
import { useAuth } from '../contexts/AuthContext';
import { FinancialService } from '../services/financialService';

interface TransactionFormData {
  type: 'income' | 'expense';
  date: string;
  description: string;
  accountNo?: string;
  category: string;
  voucherNo?: string;
  amount: number;
}

interface Transaction {
  id: string;
  type: 'income' | 'expense';
  date: string;
  description: string;
  accountNo?: string;
  category: string;
  voucherNo?: string;
  amount: number;
  createdBy: string;
  creatorName?: string;
  createdAt: string;
}

const INCOME_CATEGORIES = [
  'Loan Interest',
  'Service Charges',
  'Late Fees',
  'Membership Fees',
  'Investment Returns',
  'Other Income'
];

const EXPENSE_CATEGORIES = [
  'Office Rent',
  'Staff Salaries',
  'Utilities',
  'Office Supplies',
  'Transportation',
  'Marketing',
  'Maintenance',
  'Other Expenses'
];

export const BranchFinancials: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loadingTransactions, setLoadingTransactions] = useState(true);
  const [balanceData, setBalanceData] = useState({
    totalIncome: 0,
    totalExpense: 0,
    netBalance: 0
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset
  } = useForm<TransactionFormData>({
    defaultValues: {
      date: new Date().toISOString().split('T')[0],
      type: 'income'
    }
  });

  const transactionType = watch('type');

  useEffect(() => {
    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    try {
      setLoadingTransactions(true);
      const financialService = new FinancialService();
      const response = await financialService.getTransactions({
        page: 1,
        limit: 50,
        branchId: user?.branchId
      });
      setTransactions(response.data);
      calculateBalance(response.data);
    } catch (err) {
      console.error('Error fetching transactions:', err);
      setError('Failed to load transactions.');
    } finally {
      setLoadingTransactions(false);
    }
  };

  const calculateBalance = (transactionList: Transaction[]) => {
    const totalIncome = transactionList
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const totalExpense = transactionList
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    setBalanceData({
      totalIncome,
      totalExpense,
      netBalance: totalIncome - totalExpense
    });
  };

  const onSubmit = async (data: TransactionFormData) => {
    try {
      setLoading(true);
      setError(null);

      const financialService = new FinancialService();
      await financialService.createTransaction({
        ...data,
        amount: Number(data.amount),
        branchId: user?.branchId!
      });

      setSuccess(`${data.type === 'income' ? 'Income' : 'Expense'} entry added successfully!`);
      
      // Refresh transactions
      await fetchTransactions();
      reset({
        date: new Date().toISOString().split('T')[0],
        type: 'income'
      });

      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error('Error creating transaction:', err);
      setError(err.response?.data?.message || 'Failed to create transaction. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  const formatCurrency = (amount: number) => {
    return `৳${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD');
  };

  const getTypeColor = (type: string) => {
    return type === 'income' 
      ? 'text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400'
      : 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Branch Income & Expenditure
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage branch financial transactions and view balance reports
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Dashboard
        </button>
      </div>

      {/* Success Alert */}
      {success && (
        <Alert 
          type="success" 
          title="Success" 
          description={success}
        />
      )}

      {/* Error Alert */}
      {error && (
        <Alert 
          type="danger" 
          title="Error" 
          description={error}
          onDismiss={() => setError(null)}
          dismissible
        />
      )}

      {/* Balance Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm text-green-600 dark:text-green-400">Total Income</p>
              <p className="text-2xl font-bold text-green-700 dark:text-green-300">
                {formatCurrency(balanceData.totalIncome)}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm text-red-600 dark:text-red-400">Total Expense</p>
              <p className="text-2xl font-bold text-red-700 dark:text-red-300">
                {formatCurrency(balanceData.totalExpense)}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Net Balance</p>
              <p className={`text-2xl font-bold ${
                balanceData.netBalance >= 0 
                  ? 'text-green-700 dark:text-green-300' 
                  : 'text-red-700 dark:text-red-300'
              }`}>
                {formatCurrency(balanceData.netBalance)}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Transaction Entry Form */}
        <Card>
          <CardHeader>
            <CardTitle>Add New Transaction</CardTitle>
          </CardHeader>
          <CardContent>
            <Form onSubmit={handleSubmit(onSubmit)} spacing="lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select
                  label="Entry Type"
                  {...register('type', { required: 'Entry type is required' })}
                  error={errors.type?.message}
                  required
                  placeholder="Select entry type"
                  options={[
                    { value: 'income', label: 'Income' },
                    { value: 'expense', label: 'Expense' }
                  ]}
                />

                <Input
                  label="Date"
                  type="date"
                  {...register('date', { required: 'Date is required' })}
                  error={errors.date?.message}
                  required
                />
              </div>

              <Textarea
                label="Description"
                rows={3}
                {...register('description', { 
                  required: 'Description is required',
                  minLength: { value: 5, message: 'Description must be at least 5 characters' }
                })}
                error={errors.description?.message}
                placeholder="Enter transaction description..."
                required
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Account Number (Optional)"
                  {...register('accountNo')}
                  placeholder="Enter account number"
                />

                <Select
                  label="Category"
                  {...register('category', { required: 'Category is required' })}
                  error={errors.category?.message}
                  required
                  placeholder="Select category"
                  options={(transactionType === 'income' ? INCOME_CATEGORIES : EXPENSE_CATEGORIES).map(cat => ({
                    value: cat,
                    label: cat
                  }))}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Voucher Number (Optional)"
                  {...register('voucherNo')}
                  placeholder="Enter voucher number"
                />

                <Input
                  label="Amount (৳)"
                  type="number"
                  step="0.01"
                  min="0.01"
                  {...register('amount', { 
                    required: 'Amount is required',
                    min: { value: 0.01, message: 'Amount must be greater than 0' }
                  })}
                  error={errors.amount?.message}
                  required
                />
              </div>

              <div className="flex gap-4 pt-4">
                <Button
                  type="submit"
                  variant="primary"
                  disabled={loading}
                  className="flex-1"
                >
                  {loading ? 'Adding...' : 'Add Transaction'}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => reset({
                    date: new Date().toISOString().split('T')[0],
                    type: 'income'
                  })}
                  disabled={loading}
                >
                  Reset
                </Button>
              </div>
            </Form>
          </CardContent>
        </Card>

        {/* Transaction Listing */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            {loadingTransactions ? (
              <div className="text-center py-8">
                <Loading size="sm" />
                <p className="text-gray-500 dark:text-gray-400 mt-2">Loading transactions...</p>
              </div>
            ) : transactions.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 text-center py-8">
                No transactions found
              </p>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {transactions.map((transaction) => (
                  <div key={transaction.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(transaction.type)}`}>
                            {transaction.type}
                          </span>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {transaction.category}
                          </span>
                        </div>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {transaction.description}
                        </p>
                        <div className="grid grid-cols-2 gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                          <div>
                            <p>Date: {formatDate(transaction.date)}</p>
                            {transaction.accountNo && (
                              <p>Account: {transaction.accountNo}</p>
                            )}
                          </div>
                          <div>
                            {transaction.voucherNo && (
                              <p>Voucher: {transaction.voucherNo}</p>
                            )}
                            <p>By: {transaction.creatorName || 'Unknown'}</p>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-lg font-bold ${
                          transaction.type === 'income'
                            ? 'text-green-600 dark:text-green-400'
                            : 'text-red-600 dark:text-red-400'
                        }`}>
                          {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                {transactions.length >= 50 && (
                  <div className="text-center pt-4">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => {
                        // Navigate to full transaction history
                        navigate('/branch/transactions/history');
                      }}
                    >
                      View All Transactions
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default BranchFinancials;
