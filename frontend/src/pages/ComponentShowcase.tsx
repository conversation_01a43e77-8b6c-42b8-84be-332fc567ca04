import React, { useState } from 'react';
import {
  Button,
  Input,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Modal,
  Alert,
  Loading,
  Table,
  Form,
  FormGroup,
  FormLabel,
  Select,
  Textarea,
  ThemeToggle,
  Navigation,
} from '../components/ui';

export const ComponentShowcase: React.FC = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [alertVisible, setAlertVisible] = useState(true);

  // Sample data for table
  const tableColumns = [
    { key: 'name', title: 'Name', dataIndex: 'name', sortable: true },
    { key: 'email', title: 'Email', dataIndex: 'email', sortable: true },
    { key: 'role', title: 'Role', dataIndex: 'role' },
    {
      key: 'actions',
      title: 'Actions',
      render: () => (
        <Button size="sm" variant="outline">
          Edit
        </Button>
      ),
    },
  ];

  const tableData = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'User' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', role: 'Manager' },
  ];

  const navigationItems = [
    { key: 'home', label: 'Home', path: '/' },
    { key: 'about', label: 'About', path: '/about' },
    { key: 'services', label: 'Services', path: '/services' },
    { key: 'contact', label: 'Contact', path: '/contact' },
  ];

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-12">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-secondary-900 dark:text-secondary-100 mb-4">
          UI Components Showcase
        </h1>
        <p className="text-lg text-secondary-600 dark:text-secondary-400">
          A comprehensive collection of reusable UI components
        </p>
      </div>

      {/* Theme Toggle Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-6 text-secondary-900 dark:text-secondary-100">
          Theme Toggle
        </h2>
        <Card>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <ThemeToggle variant="icon" />
              <ThemeToggle variant="button" showLabel />
              <ThemeToggle variant="switch" showLabel />
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Buttons Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-6 text-secondary-900 dark:text-secondary-100">
          Buttons
        </h2>
        <Card>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-3">Variants</h3>
              <div className="flex flex-wrap gap-3">
                <Button variant="primary">Primary</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="danger">Danger</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="link">Link</Button>
              </div>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-3">Sizes</h3>
              <div className="flex flex-wrap items-center gap-3">
                <Button size="sm">Small</Button>
                <Button size="md">Medium</Button>
                <Button size="lg">Large</Button>
                <Button size="xl">Extra Large</Button>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3">States</h3>
              <div className="flex flex-wrap gap-3">
                <Button loading>Loading</Button>
                <Button disabled>Disabled</Button>
                <Button fullWidth>Full Width</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Inputs Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-6 text-secondary-900 dark:text-secondary-100">
          Form Inputs
        </h2>
        <Card>
          <CardContent>
            <Form spacing="lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Text Input"
                  placeholder="Enter text"
                  helperText="This is helper text"
                />
                
                <Input
                  label="Email Input"
                  type="email"
                  placeholder="<EMAIL>"
                  leftIcon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                    </svg>
                  }
                />

                <Input
                  label="Password Input"
                  type="password"
                  placeholder="Enter password"
                  showPasswordToggle
                />

                <Input
                  label="Input with Error"
                  placeholder="This has an error"
                  error="This field is required"
                />

                <Select
                  label="Select Input"
                  placeholder="Choose an option"
                  options={[
                    { value: 'option1', label: 'Option 1' },
                    { value: 'option2', label: 'Option 2' },
                    { value: 'option3', label: 'Option 3' },
                  ]}
                />

                <div className="md:col-span-2">
                  <Textarea
                    label="Textarea"
                    placeholder="Enter your message"
                    rows={4}
                  />
                </div>
              </div>
            </Form>
          </CardContent>
        </Card>
      </section>

      {/* Cards Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-6 text-secondary-900 dark:text-secondary-100">
          Cards
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card variant="default">
            <CardHeader>
              <CardTitle>Default Card</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-secondary-600 dark:text-secondary-400">
                This is a default card with standard styling.
              </p>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardHeader>
              <CardTitle>Outlined Card</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-secondary-600 dark:text-secondary-400">
                This card has a prominent border outline.
              </p>
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardHeader>
              <CardTitle>Elevated Card</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-secondary-600 dark:text-secondary-400">
                This card has an elevated shadow effect.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Alerts Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-6 text-secondary-900 dark:text-secondary-100">
          Alerts
        </h2>
        <div className="space-y-4">
          <Alert
            type="info"
            title="Information"
            description="This is an informational alert message."
          />
          
          <Alert
            type="success"
            title="Success"
            description="Your action was completed successfully."
          />
          
          <Alert
            type="warning"
            title="Warning"
            description="Please review this important information."
          />
          
          {alertVisible && (
            <Alert
              type="danger"
              title="Error"
              description="Something went wrong. Please try again."
              dismissible
              onDismiss={() => setAlertVisible(false)}
            />
          )}
        </div>
      </section>

      {/* Loading Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-6 text-secondary-900 dark:text-secondary-100">
          Loading States
        </h2>
        <Card>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-3">Spinners</h3>
              <div className="flex items-center gap-6">
                <Loading variant="spinner" size="sm" />
                <Loading variant="spinner" size="md" />
                <Loading variant="spinner" size="lg" />
                <Loading variant="spinner" size="xl" />
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3">Other Variants</h3>
              <div className="flex items-center gap-6">
                <Loading variant="dots" size="md" />
                <Loading variant="pulse" size="md" />
                <Loading variant="spinner" size="md" text="Loading..." />
              </div>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Table Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-6 text-secondary-900 dark:text-secondary-100">
          Data Table
        </h2>
        <Card>
          <CardContent>
            <Table
              columns={tableColumns}
              data={tableData}
              searchable
              pagination={{
                current: 1,
                pageSize: 10,
                total: tableData.length,
                onChange: (page, pageSize) => console.log(page, pageSize),
              }}
            />
          </CardContent>
        </Card>
      </section>

      {/* Navigation Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-6 text-secondary-900 dark:text-secondary-100">
          Navigation
        </h2>
        <Card>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-3">Horizontal Navigation</h3>
              <Navigation items={navigationItems} orientation="horizontal" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-3">Pills Variant</h3>
              <Navigation items={navigationItems} variant="pills" />
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Modal Section */}
      <section>
        <h2 className="text-2xl font-semibold mb-6 text-secondary-900 dark:text-secondary-100">
          Modal
        </h2>
        <Card>
          <CardContent>
            <Button onClick={() => setModalOpen(true)}>
              Open Modal
            </Button>
            
            <Modal
              isOpen={modalOpen}
              onClose={() => setModalOpen(false)}
              title="Example Modal"
              description="This is an example modal dialog."
            >
              <div className="space-y-4">
                <p className="text-secondary-600 dark:text-secondary-400">
                  This modal demonstrates the modal component with a title, description, and custom content.
                </p>
                
                <div className="flex justify-end space-x-3">
                  <Button variant="outline" onClick={() => setModalOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="primary" onClick={() => setModalOpen(false)}>
                    Confirm
                  </Button>
                </div>
              </div>
            </Modal>
          </CardContent>
        </Card>
      </section>
    </div>
  );
};
