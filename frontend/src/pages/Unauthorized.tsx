import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Card, CardContent, Button } from '../components/ui';

export const Unauthorized: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    if (user) {
      // Redirect based on user role
      switch (user.role) {
        case 'admin':
          navigate('/admin/dashboard');
          break;
        case 'manager':
          navigate('/manager/dashboard');
          break;
        case 'field_officer':
          navigate('/field-officer/dashboard');
          break;
        case 'member':
          navigate('/member/dashboard');
          break;
        default:
          navigate('/dashboard');
      }
    } else {
      navigate('/');
    }
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-secondary-50 dark:bg-secondary-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <Card className="text-center">
          <CardContent className="py-12">
            {/* 403 Icon */}
            <div className="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-danger-100 dark:bg-danger-900 mb-6">
              <svg
                className="h-12 w-12 text-danger-600 dark:text-danger-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>

            <h1 className="text-3xl font-bold text-secondary-900 dark:text-secondary-100 mb-2">
              Access Denied
            </h1>
            
            <p className="text-secondary-600 dark:text-secondary-400 mb-6">
              You don't have permission to access this page. Please contact your administrator if you believe this is an error.
            </p>

            {user && (
              <div className="bg-secondary-100 dark:bg-secondary-800 rounded-lg p-4 mb-6">
                <p className="text-sm text-secondary-700 dark:text-secondary-300">
                  <span className="font-medium">Current Role:</span> {user.role.replace('_', ' ').toUpperCase()}
                </p>
                {user.memberId && (
                  <p className="text-sm text-secondary-700 dark:text-secondary-300">
                    <span className="font-medium">Member ID:</span> {user.memberId}
                  </p>
                )}
              </div>
            )}

            <div className="space-y-3">
              <Button
                variant="primary"
                size="lg"
                fullWidth
                onClick={handleGoHome}
              >
                Go to Dashboard
              </Button>
              
              <Button
                variant="outline"
                size="lg"
                fullWidth
                onClick={handleGoBack}
              >
                Go Back
              </Button>

              {user && (
                <Button
                  variant="ghost"
                  size="lg"
                  fullWidth
                  onClick={handleLogout}
                  className="text-danger-600 hover:text-danger-700 hover:bg-danger-50 dark:text-danger-400 dark:hover:text-danger-300 dark:hover:bg-danger-900"
                >
                  Sign Out
                </Button>
              )}
            </div>

            <div className="mt-8 pt-6 border-t border-secondary-200 dark:border-secondary-700">
              <p className="text-xs text-secondary-500 dark:text-secondary-400">
                Need help? Contact your system administrator or{' '}
                <Link
                  to="/support"
                  className="text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300"
                >
                  visit our support page
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
