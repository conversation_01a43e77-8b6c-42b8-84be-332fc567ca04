import React from 'react';
import { Link } from 'react-router-dom';
import { APP_CONFIG } from '../utils/constants';

export const Home: React.FC = () => {
  return (
    <div className="text-center">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 mb-6">
          Welcome to {APP_CONFIG.NAME}
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          {APP_CONFIG.DESCRIPTION}
        </p>
        
        <div className="grid md:grid-cols-3 gap-8 mt-12">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Modern Stack</h3>
            <p className="text-gray-600">
              Built with React 19, TypeScript, and Tailwind CSS for a modern development experience.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Full-Stack</h3>
            <p className="text-gray-600">
              Complete backend with Node.js, Express, and MySQL for robust data management.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="text-lg font-semibold mb-4">Production Ready</h3>
            <p className="text-gray-600">
              Docker configuration and deployment scripts for easy VPS deployment.
            </p>
          </div>
        </div>

        <div className="mt-12">
          <Link to="/register" className="btn-primary mr-4">
            Get Started
          </Link>
          <Link to="/login" className="btn-secondary">
            Sign In
          </Link>
        </div>
      </div>
    </div>
  );
};
