import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent, Alert } from '../components/ui';
import { MemberRegistrationForm } from '../components/members';
import { Branch } from '../types';
import { useAuth } from '../contexts/AuthContext';
import { branchService } from '../services/branchService';

export const MemberRegistration: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBranches = async () => {
      try {
        setLoading(true);
        const response = await branchService.getBranches();
        setBranches(response.data || []); // Ensure branches is always an array
      } catch (err) {
        console.error('Error fetching branches:', err);
        setError('Failed to load branches. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchBranches();
  }, []);

  const handleSuccess = (member: any) => {
    // Navigate back to dashboard or member list
    navigate('/dashboard', { 
      state: { 
        message: `Member ${member.name} registered successfully!`,
        type: 'success'
      }
    });
  };

  const handleCancel = () => {
    navigate('/dashboard');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading branches...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Member Registration
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Register a new member in the system
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Dashboard
        </button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert 
          type="danger" 
          title="Error" 
          description={error}
          onDismiss={() => setError(null)}
          dismissible
        />
      )}

      {/* Registration Form */}
      <Card>
        <CardHeader>
          <CardTitle>New Member Information</CardTitle>
        </CardHeader>
        <CardContent>
          <MemberRegistrationForm
            onSuccess={handleSuccess}
            onCancel={handleCancel}
            branches={branches}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default MemberRegistration;
