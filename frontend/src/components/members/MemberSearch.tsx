import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Input } from '../ui';
import { MemberSearchResult, MemberSearchQuery } from '../../types';
import MemberService from '../../services/memberService';

interface MemberSearchProps {
  value?: MemberSearchResult | null;
  onChange: (member: MemberSearchResult | null) => void;
  placeholder?: string;
  branchId?: string;
  disabled?: boolean;
  className?: string;
  label?: string;
  error?: string;
  required?: boolean;
}

export const MemberSearch: React.FC<MemberSearchProps> = ({
  value,
  onChange,
  placeholder = 'Search member by name, ID, or phone...',
  branchId,
  disabled = false,
  className = '',
  label,
  error,
  required = false
}) => {
  const [query, setQuery] = useState(value ? `${value.name} (${value.memberId})` : '');
  const [results, setResults] = useState<MemberSearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  // Debounced search function
  const searchMembers = useCallback(async (searchQuery: string) => {
    if (searchQuery.length < 2) {
      setResults([]);
      setIsOpen(false);
      return;
    }

    setLoading(true);
    try {
      const searchParams: MemberSearchQuery = {
        query: searchQuery,
        branchId,
        limit: 10
      };
      
      const members = await MemberService.searchMembers(searchParams);
      setResults(members);
      setIsOpen(true);
      setSelectedIndex(-1);
    } catch (error) {
      console.error('Error searching members:', error);
      setResults([]);
    } finally {
      setLoading(false);
    }
  }, [branchId]);

  // Handle input change with debouncing
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);

    // Clear selection if query doesn't match current value
    if (value && !newQuery.includes(value.memberId)) {
      onChange(null);
    }

    // Clear previous debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Debounce search
    debounceRef.current = setTimeout(() => {
      searchMembers(newQuery);
    }, 300);
  };

  // Handle member selection
  const handleSelectMember = (member: MemberSearchResult) => {
    setQuery(`${member.name} (${member.memberId})`);
    onChange(member);
    setIsOpen(false);
    setResults([]);
    setSelectedIndex(-1);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < results.length) {
          handleSelectMember(results[selectedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        break;
    }
  };

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Clear debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  // Update query when value changes externally
  useEffect(() => {
    if (value) {
      setQuery(`${value.name} (${value.memberId})`);
    } else {
      setQuery('');
    }
  }, [value]);

  return (
    <div className={`relative ${className}`}>
      <Input
        ref={inputRef}
        label={label}
        value={query}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        error={error}
        required={required}
        icon={loading ? '⏳' : '🔍'}
        className="pr-10"
      />

      {/* Dropdown */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto"
        >
          {loading ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
              <div className="inline-block w-4 h-4 border-2 border-gray-300 border-t-primary-500 rounded-full animate-spin mr-2"></div>
              Searching...
            </div>
          ) : results.length > 0 ? (
            <div className="py-1">
              {results.map((member, index) => (
                <button
                  key={member.id}
                  type="button"
                  onClick={() => handleSelectMember(member)}
                  className={`
                    w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors
                    ${index === selectedIndex ? 'bg-primary-50 dark:bg-primary-900/20' : ''}
                  `}
                >
                  <div className="flex items-center gap-3">
                    {member.photo ? (
                      <img
                        src={member.photo}
                        alt={member.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                        <span className="text-gray-500 dark:text-gray-400 text-lg">👤</span>
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 dark:text-white truncate">
                        {member.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        ID: {member.memberId} • {member.phoneNumber}
                      </div>
                      {member.branchName && (
                        <div className="text-xs text-gray-400 dark:text-gray-500">
                          {member.branchName}
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          ) : query.length >= 2 ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
              <div className="text-2xl mb-2">🔍</div>
              <p>No members found</p>
              <p className="text-sm">Try searching with a different term</p>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default MemberSearch;
