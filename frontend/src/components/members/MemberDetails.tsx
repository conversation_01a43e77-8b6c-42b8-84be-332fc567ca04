import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  Button,
  Loading,
  Alert,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter
} from '../ui';
import { Member } from '../../types';
import MemberService from '../../services/memberService';

interface MemberDetailsProps {
  memberId: string;
  onEdit?: (member: Member) => void;
  onDelete?: (member: Member) => void;
  onClose?: () => void;
  className?: string;
}

export const MemberDetails: React.FC<MemberDetailsProps> = ({
  memberId,
  onEdit,
  onDelete,
  onClose,
  className = ''
}) => {
  const [member, setMember] = useState<Member | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadMember();
  }, [memberId]);

  const loadMember = async () => {
    setLoading(true);
    setError(null);

    try {
      const memberData = await MemberService.getMemberById(memberId);
      setMember(memberData);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load member details');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!member) return;

    setDeleting(true);
    try {
      await MemberService.deleteMember(member.id);
      setShowDeleteModal(false);
      if (onDelete) {
        onDelete(member);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete member');
    } finally {
      setDeleting(false);
    }
  };

  const handlePrintCard = async () => {
    if (!member) return;

    try {
      const blob = await MemberService.printMemberCard(member.id);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `member-card-${member.memberId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error printing member card:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className={className}>
        <Card>
          <CardContent className="p-8">
            <Loading size="lg" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !member) {
    return (
      <div className={className}>
        <Card>
          <CardContent className="p-8">
            <Alert 
              type="error" 
              title="Error" 
              description={error || 'Member not found'} 
            />
            {onClose && (
              <Button variant="secondary" onClick={onClose} className="mt-4">
                Go Back
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <CardTitle className="flex items-center gap-2">
              <span>👤</span>
              Member Details
            </CardTitle>
            
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={handlePrintCard}
              >
                🖨️ Print Card
              </Button>
              
              {onEdit && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={() => onEdit(member)}
                >
                  ✏️ Edit
                </Button>
              )}
              
              {onDelete && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => setShowDeleteModal(true)}
                  className="text-red-600 hover:text-red-700"
                >
                  🗑️ Delete
                </Button>
              )}
              
              {onClose && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={onClose}
                >
                  ✕ Close
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Photo and Basic Info */}
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-48 h-48 mx-auto rounded-lg overflow-hidden bg-gray-200 dark:bg-gray-600 mb-4">
                  {member.photo ? (
                    <img 
                      src={member.photo} 
                      alt={member.name} 
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400 text-6xl">
                      👤
                    </div>
                  )}
                </div>
                
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {member.name}
                </h2>
                
                <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                  <p>Member ID: <span className="font-medium">{member.memberId}</span></p>
                  <p>Phone: <span className="font-medium">{member.phoneNumber}</span></p>
                  <p>
                    Status: 
                    <span className={`ml-1 px-2 py-1 rounded-full text-xs font-medium ${
                      member.isActive 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                        : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                    }`}>
                      {member.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </p>
                </div>
              </div>
            </div>

            {/* Personal Information */}
            <div className="lg:col-span-2 space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                  Personal Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Father's/Husband's Name
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.fatherOrHusbandName}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Mother's Name
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.motherName}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Date of Birth
                    </label>
                    <p className="text-gray-900 dark:text-white">{formatDate(member.dateOfBirth)}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Religion
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.religion}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Blood Group
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.bloodGroup || 'Not specified'}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Occupation
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.occupation}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      NID Number
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.nidNumber}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Branch
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.branchName || 'N/A'}</p>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                  Address Information
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Present Address
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.presentAddress}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Permanent Address
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.permanentAddress}</p>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 border-b border-gray-200 dark:border-gray-700 pb-2">
                  Additional Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Reference Member
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.referenceName || 'No reference'}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Created By
                    </label>
                    <p className="text-gray-900 dark:text-white">{member.creatorName || 'N/A'}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Registration Date
                    </label>
                    <p className="text-gray-900 dark:text-white">{formatDate(member.createdAt)}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Last Updated
                    </label>
                    <p className="text-gray-900 dark:text-white">{formatDate(member.updatedAt)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)}>
        <ModalHeader>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Delete Member
          </h3>
        </ModalHeader>
        <ModalBody>
          <p className="text-gray-600 dark:text-gray-400">
            Are you sure you want to delete <strong>{member.name}</strong>? 
            This action cannot be undone and will remove all associated data.
          </p>
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setShowDeleteModal(false)}
            disabled={deleting}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleDelete}
            disabled={deleting}
            className="bg-red-600 hover:bg-red-700"
          >
            {deleting ? 'Deleting...' : 'Delete Member'}
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default MemberDetails;
