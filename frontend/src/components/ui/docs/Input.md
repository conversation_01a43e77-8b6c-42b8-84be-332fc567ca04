# Input Component

A comprehensive form input component with validation, icons, and password toggle functionality.

## Import

```tsx
import { Input } from '../components/ui';
```

## Basic Usage

```tsx
<Input 
  label="Email"
  type="email"
  placeholder="Enter your email"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Input label |
| `error` | `string` | - | Error message to display |
| `helperText` | `string` | - | Helper text below input |
| `leftIcon` | `ReactNode` | - | Icon on the left side |
| `rightIcon` | `ReactNode` | - | Icon on the right side |
| `size` | `'sm' \| 'md' \| 'lg'` | `'md'` | Input size |
| `variant` | `'default' \| 'filled'` | `'default'` | Input style variant |
| `showPasswordToggle` | `boolean` | `false` | Show password visibility toggle |
| `type` | `InputType` | `'text'` | Input type |

## Input Types

Supports all standard HTML input types:
- `text`, `email`, `password`, `number`
- `tel`, `url`, `search`
- `date`, `datetime-local`, `time`

## Basic Examples

### Text Input
```tsx
<Input 
  label="Full Name"
  placeholder="Enter your full name"
  required
/>
```

### Email Input
```tsx
<Input 
  label="Email Address"
  type="email"
  placeholder="<EMAIL>"
  leftIcon={<MailIcon />}
/>
```

### Password Input
```tsx
<Input 
  label="Password"
  type="password"
  showPasswordToggle
  placeholder="Enter your password"
/>
```

### Number Input
```tsx
<Input 
  label="Age"
  type="number"
  min="18"
  max="100"
  placeholder="Enter your age"
/>
```

## With Validation

```tsx
<Input 
  label="Email"
  type="email"
  error="Please enter a valid email address"
  placeholder="<EMAIL>"
/>
```

## With Helper Text

```tsx
<Input 
  label="Username"
  helperText="Username must be 3-20 characters long"
  placeholder="Enter username"
/>
```

## With Icons

```tsx
<Input 
  label="Search"
  type="search"
  placeholder="Search users..."
  leftIcon={<SearchIcon />}
  rightIcon={<FilterIcon />}
/>
```

## Sizes

```tsx
<Input size="sm" placeholder="Small input" />
<Input size="md" placeholder="Medium input" />
<Input size="lg" placeholder="Large input" />
```

## Variants

### Default
```tsx
<Input 
  variant="default"
  placeholder="Default variant"
/>
```

### Filled
```tsx
<Input 
  variant="filled"
  placeholder="Filled variant"
/>
```

## Form Integration

### With React Hook Form
```tsx
import { useForm } from 'react-hook-form';

function MyForm() {
  const { register, formState: { errors } } = useForm();

  return (
    <form>
      <Input
        label="Email"
        type="email"
        error={errors.email?.message}
        {...register('email', { 
          required: 'Email is required',
          pattern: {
            value: /^\S+@\S+$/i,
            message: 'Invalid email address'
          }
        })}
      />
    </form>
  );
}
```

### Controlled Component
```tsx
function ControlledInput() {
  const [value, setValue] = useState('');

  return (
    <Input
      label="Controlled Input"
      value={value}
      onChange={(e) => setValue(e.target.value)}
      placeholder="Type something..."
    />
  );
}
```

## Accessibility

- Proper label association with `htmlFor` and `id`
- ARIA attributes for error states
- Screen reader support for helper text and errors
- Keyboard navigation support
- Focus indicators

## Custom Styling

```tsx
<Input 
  label="Custom Input"
  className="border-purple-300 focus:border-purple-500"
  placeholder="Custom styled input"
/>
```

## Advanced Examples

### Search Input with Debounce
```tsx
function SearchInput({ onSearch }) {
  const [query, setQuery] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      onSearch(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query, onSearch]);

  return (
    <Input
      type="search"
      placeholder="Search..."
      value={query}
      onChange={(e) => setQuery(e.target.value)}
      leftIcon={<SearchIcon />}
    />
  );
}
```

### Password Strength Indicator
```tsx
function PasswordInput() {
  const [password, setPassword] = useState('');
  const strength = calculatePasswordStrength(password);

  return (
    <div>
      <Input
        label="Password"
        type="password"
        showPasswordToggle
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />
      <div className="mt-2">
        <div className="text-xs text-gray-600">
          Strength: {strength.label}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-1">
          <div 
            className={`h-1 rounded-full ${strength.color}`}
            style={{ width: `${(strength.score / 5) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
}
```
