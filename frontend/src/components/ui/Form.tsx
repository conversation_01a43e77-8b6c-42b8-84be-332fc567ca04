import React, { forwardRef } from 'react';
import { cn } from '../../utils/cn';

// Form Container
export interface FormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode;
  spacing?: 'sm' | 'md' | 'lg';
}

const formSpacing = {
  sm: 'space-y-3',
  md: 'space-y-4',
  lg: 'space-y-6',
};

export const Form = forwardRef<HTMLFormElement, FormProps>(
  ({ className, children, spacing = 'md', ...props }, ref) => {
    return (
      <form
        ref={ref}
        className={cn('w-full', formSpacing[spacing], className)}
        {...props}
      >
        {children}
      </form>
    );
  }
);

Form.displayName = 'Form';

// Form Group
export interface FormGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  inline?: boolean;
}

export const FormGroup = forwardRef<HTMLDivElement, FormGroupProps>(
  ({ className, children, inline = false, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          inline ? 'flex items-center space-x-4' : 'space-y-1',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

FormGroup.displayName = 'FormGroup';

// Form Label
export interface FormLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  children: React.ReactNode;
  required?: boolean;
  optional?: boolean;
}

export const FormLabel = forwardRef<HTMLLabelElement, FormLabelProps>(
  ({ className, children, required = false, optional = false, ...props }, ref) => {
    return (
      <label
        ref={ref}
        className={cn(
          'block text-sm font-medium text-secondary-700 dark:text-secondary-300',
          className
        )}
        {...props}
      >
        {children}
        {required && <span className="text-danger-500 ml-1">*</span>}
        {optional && <span className="text-secondary-400 ml-1 font-normal">(optional)</span>}
      </label>
    );
  }
);

FormLabel.displayName = 'FormLabel';

// Form Error Message
export interface FormErrorProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
}

export const FormError = forwardRef<HTMLParagraphElement, FormErrorProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn('text-sm text-danger-600 dark:text-danger-400', className)}
        role="alert"
        {...props}
      >
        {children}
      </p>
    );
  }
);

FormError.displayName = 'FormError';

// Form Helper Text
export interface FormHelperProps extends React.HTMLAttributes<HTMLParagraphElement> {
  children: React.ReactNode;
}

export const FormHelper = forwardRef<HTMLParagraphElement, FormHelperProps>(
  ({ className, children, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn('text-sm text-secondary-500 dark:text-secondary-400', className)}
        {...props}
      >
        {children}
      </p>
    );
  }
);

FormHelper.displayName = 'FormHelper';

// Select Component
export interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  helperText?: string;
  options?: Array<{ value: string | number; label: string; disabled?: boolean }>;
  placeholder?: string;
  children?: React.ReactNode;
}

export const Select = forwardRef<HTMLSelectElement, SelectProps>(
  (
    {
      className,
      label,
      error,
      helperText,
      options,
      placeholder,
      required,
      disabled,
      id,
      children,
      ...props
    },
    ref
  ) => {
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;

    return (
      <FormGroup>
        {label && (
          <FormLabel htmlFor={selectId} required={required}>
            {label}
          </FormLabel>
        )}

        <select
          ref={ref}
          id={selectId}
          className={cn(
            'w-full px-3 py-2 text-sm border rounded-lg transition-all duration-200 focus:outline-hidden focus:ring-2 focus:ring-offset-1',
            error
              ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500'
              : 'border-secondary-300 bg-white focus:border-primary-500 focus:ring-primary-500 dark:border-secondary-600 dark:bg-secondary-800 dark:text-white',
            disabled && 'opacity-50 cursor-not-allowed bg-secondary-50 dark:bg-secondary-900',
            className
          )}
          disabled={disabled}
          required={required}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${selectId}-error` : helperText ? `${selectId}-helper` : undefined}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options ? options.map((option) => (
            <option
              key={option.value}
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          )) : children}
        </select>
        
        {error && (
          <FormError id={`${selectId}-error`}>
            {error}
          </FormError>
        )}
        
        {helperText && !error && (
          <FormHelper id={`${selectId}-helper`}>
            {helperText}
          </FormHelper>
        )}
      </FormGroup>
    );
  }
);

Select.displayName = 'Select';

// Textarea Component
export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      label,
      error,
      helperText,
      resize = 'vertical',
      required,
      disabled,
      id,
      ...props
    },
    ref
  ) => {
    const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;

    const resizeClasses = {
      none: 'resize-none',
      vertical: 'resize-y',
      horizontal: 'resize-x',
      both: 'resize',
    };

    return (
      <FormGroup>
        {label && (
          <FormLabel htmlFor={textareaId} required={required}>
            {label}
          </FormLabel>
        )}
        
        <textarea
          ref={ref}
          id={textareaId}
          className={cn(
            'w-full px-3 py-2 text-sm border rounded-lg transition-all duration-200 focus:outline-hidden focus:ring-2 focus:ring-offset-1 min-h-[80px]',
            resizeClasses[resize],
            error
              ? 'border-danger-300 focus:border-danger-500 focus:ring-danger-500'
              : 'border-secondary-300 bg-white focus:border-primary-500 focus:ring-primary-500 dark:border-secondary-600 dark:bg-secondary-800 dark:text-white',
            disabled && 'opacity-50 cursor-not-allowed bg-secondary-50 dark:bg-secondary-900',
            className
          )}
          disabled={disabled}
          required={required}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${textareaId}-error` : helperText ? `${textareaId}-helper` : undefined}
          {...props}
        />
        
        {error && (
          <FormError id={`${textareaId}-error`}>
            {error}
          </FormError>
        )}
        
        {helperText && !error && (
          <FormHelper id={`${textareaId}-helper`}>
            {helperText}
          </FormHelper>
        )}
      </FormGroup>
    );
  }
);

Textarea.displayName = 'Textarea';
