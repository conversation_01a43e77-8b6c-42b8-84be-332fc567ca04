# UI Components Library

A comprehensive, accessible, and modern React UI components library built with TypeScript, Tailwind CSS, and dark mode support.

## Features

- 🎨 **Modern Design**: Clean, professional design with consistent styling
- 🌙 **Dark Mode**: Full dark mode support with smooth transitions
- ♿ **Accessibility**: ARIA labels, keyboard navigation, and screen reader support
- 📱 **Responsive**: Mobile-first design with responsive breakpoints
- 🎯 **TypeScript**: Full TypeScript support with comprehensive type definitions
- 🎨 **Customizable**: Easy to customize with Tailwind CSS classes
- 🔧 **Flexible**: Composable components with sensible defaults

## Components Overview

### Core Components

- **Button** - Versatile button component with multiple variants and sizes
- **Input** - Form input with validation, icons, and password toggle
- **Card** - Flexible container with header, content, and footer sections
- **Modal** - Accessible modal dialogs with backdrop and keyboard support
- **Alert** - Notification component with different types and dismissible option
- **Loading** - Loading indicators including spinners, dots, and skeletons

### Advanced Components

- **Table** - Data table with sorting, pagination, and search functionality
- **Form** - Form components with validation and error handling
- **Sidebar** - Collapsible navigation sidebar with nested items
- **Navigation** - Responsive navigation component with mobile support
- **ThemeToggle** - Theme switching component with multiple variants

## Installation

The components are already included in this project. To use them in your components:

```tsx
import { Button, Card, Input, Modal } from '../components/ui';
```

## Quick Start

### Basic Button Usage

```tsx
import { Button } from '../components/ui';

function MyComponent() {
  return (
    <div className="space-x-4">
      <Button variant="primary">Primary Button</Button>
      <Button variant="secondary">Secondary Button</Button>
      <Button variant="outline">Outline Button</Button>
      <Button variant="danger">Danger Button</Button>
    </div>
  );
}
```

### Form with Validation

```tsx
import { Form, Input, Button, Alert } from '../components/ui';
import { useForm } from 'react-hook-form';

function LoginForm() {
  const { register, handleSubmit, formState: { errors } } = useForm();

  const onSubmit = (data) => {
    console.log(data);
  };

  return (
    <Form onSubmit={handleSubmit(onSubmit)}>
      <Input
        label="Email"
        type="email"
        error={errors.email?.message}
        {...register('email', { required: 'Email is required' })}
      />
      
      <Input
        label="Password"
        type="password"
        showPasswordToggle
        error={errors.password?.message}
        {...register('password', { required: 'Password is required' })}
      />
      
      <Button type="submit" variant="primary" fullWidth>
        Sign In
      </Button>
    </Form>
  );
}
```

### Data Table

```tsx
import { Table } from '../components/ui';

function UserTable() {
  const columns = [
    { key: 'name', title: 'Name', dataIndex: 'name', sortable: true },
    { key: 'email', title: 'Email', dataIndex: 'email', sortable: true },
    { key: 'role', title: 'Role', dataIndex: 'role' },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record) => (
        <Button size="sm" variant="outline">
          Edit
        </Button>
      ),
    },
  ];

  const data = [
    { id: 1, name: 'John Doe', email: '<EMAIL>', role: 'Admin' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User' },
  ];

  return (
    <Table
      columns={columns}
      data={data}
      searchable
      pagination={{
        current: 1,
        pageSize: 10,
        total: data.length,
        onChange: (page, pageSize) => console.log(page, pageSize),
      }}
    />
  );
}
```

### Modal Dialog

```tsx
import { Modal, Button } from '../components/ui';
import { useState } from 'react';

function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <Button onClick={() => setIsOpen(true)}>
        Open Modal
      </Button>
      
      <Modal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title="Confirm Action"
        description="Are you sure you want to proceed?"
      >
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={() => setIsOpen(false)}>
            Confirm
          </Button>
        </div>
      </Modal>
    </>
  );
}
```

## Theme System

The components support both light and dark themes. Use the `ThemeProvider` and `ThemeToggle` components:

```tsx
import { ThemeProvider, ThemeToggle } from '../components/ui';

function App() {
  return (
    <ThemeProvider>
      <div className="min-h-screen bg-white dark:bg-gray-900">
        <header className="flex justify-between items-center p-4">
          <h1>My App</h1>
          <ThemeToggle variant="button" showLabel />
        </header>
        {/* Your app content */}
      </div>
    </ThemeProvider>
  );
}
```

## Accessibility

All components follow accessibility best practices:

- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **ARIA Labels**: Proper ARIA attributes for screen readers
- **Focus Management**: Visible focus indicators and logical tab order
- **Color Contrast**: WCAG compliant color combinations
- **Semantic HTML**: Proper HTML semantics for better accessibility

## Customization

Components can be customized using Tailwind CSS classes:

```tsx
<Button 
  variant="primary" 
  className="bg-purple-600 hover:bg-purple-700"
>
  Custom Purple Button
</Button>
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

When adding new components:

1. Follow the existing patterns and conventions
2. Include TypeScript types
3. Add proper accessibility attributes
4. Support both light and dark themes
5. Include comprehensive documentation
6. Write unit tests

## Component Documentation

For detailed documentation of individual components, see:

- [Button](./docs/Button.md) - Button component with variants and states
- [Input](./docs/Input.md) - Form input with validation and icons
- [Card](./docs/Card.md) - Flexible container component
- [Modal](./docs/Modal.md) - Accessible modal dialogs
- [Alert](./docs/Alert.md) - Notification and alert component
- [Loading](./docs/Loading.md) - Loading indicators and skeletons
- [Table](./docs/Table.md) - Data table with advanced features
- [Form](./docs/Form.md) - Form components and validation
- [Sidebar](./docs/Sidebar.md) - Navigation sidebar component
- [Navigation](./docs/Navigation.md) - Responsive navigation
- [ThemeToggle](./docs/ThemeToggle.md) - Theme switching component

## License

This UI library is part of the Sonali App project.
