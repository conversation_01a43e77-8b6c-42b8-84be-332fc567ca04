import React, { forwardRef } from 'react';
import { cn } from '../../utils/cn';

export type AlertType = 'info' | 'success' | 'warning' | 'danger';

export interface AlertProps extends React.HTMLAttributes<HTMLDivElement> {
  type?: AlertType;
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  dismissible?: boolean;
  onDismiss?: () => void;
  children?: React.ReactNode;
}

const alertVariants = {
  info: {
    container: 'bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800',
    icon: 'text-blue-600 dark:text-blue-400',
    title: 'text-blue-800 dark:text-blue-200',
    description: 'text-blue-700 dark:text-blue-300',
  },
  success: {
    container: 'bg-success-50 border-success-200 dark:bg-success-950 dark:border-success-800',
    icon: 'text-success-600 dark:text-success-400',
    title: 'text-success-800 dark:text-success-200',
    description: 'text-success-700 dark:text-success-300',
  },
  warning: {
    container: 'bg-warning-50 border-warning-200 dark:bg-warning-950 dark:border-warning-800',
    icon: 'text-warning-600 dark:text-warning-400',
    title: 'text-warning-800 dark:text-warning-200',
    description: 'text-warning-700 dark:text-warning-300',
  },
  danger: {
    container: 'bg-danger-50 border-danger-200 dark:bg-danger-950 dark:border-danger-800',
    icon: 'text-danger-600 dark:text-danger-400',
    title: 'text-danger-800 dark:text-danger-200',
    description: 'text-danger-700 dark:text-danger-300',
  },
};

const defaultIcons = {
  info: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
    </svg>
  ),
  success: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
    </svg>
  ),
  warning: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
    </svg>
  ),
  danger: (
    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
    </svg>
  ),
};

export const Alert = forwardRef<HTMLDivElement, AlertProps>(
  (
    {
      className,
      type = 'info',
      title,
      description,
      icon,
      dismissible = false,
      onDismiss,
      children,
      ...props
    },
    ref
  ) => {
    const variant = alertVariants[type];
    const defaultIcon = defaultIcons[type];

    return (
      <div
        ref={ref}
        className={cn(
          'relative rounded-lg border p-4 transition-all duration-200',
          variant.container,
          className
        )}
        role="alert"
        {...props}
      >
        <div className="flex">
          <div className="shrink-0">
            <div className={cn(variant.icon)}>
              {icon || defaultIcon}
            </div>
          </div>
          
          <div className="ml-3 flex-1">
            {title && (
              <h3 className={cn('text-sm font-medium', variant.title)}>
                {title}
              </h3>
            )}
            
            {description && (
              <div className={cn('text-sm', title ? 'mt-1' : '', variant.description)}>
                {description}
              </div>
            )}
            
            {children && (
              <div className={cn('text-sm', (title || description) ? 'mt-2' : '', variant.description)}>
                {children}
              </div>
            )}
          </div>
          
          {dismissible && onDismiss && (
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  type="button"
                  onClick={onDismiss}
                  className={cn(
                    'inline-flex rounded-md p-1.5 focus:outline-hidden focus:ring-2 focus:ring-offset-2 transition-colors',
                    variant.icon,
                    'hover:bg-black/5 focus:ring-current dark:hover:bg-white/5'
                  )}
                  aria-label="Dismiss alert"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
);

Alert.displayName = 'Alert';
