import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '../../utils/cn';

export interface SidebarItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path?: string;
  onClick?: () => void;
  children?: SidebarItem[];
  badge?: string | number;
  disabled?: boolean;
}

export interface SidebarProps {
  items: SidebarItem[];
  collapsed?: boolean;
  onCollapse?: (collapsed: boolean) => void;
  className?: string;
  width?: string;
  collapsedWidth?: string;
  logo?: React.ReactNode;
  footer?: React.ReactNode;
}

export const Sidebar: React.FC<SidebarProps> = ({
  items,
  collapsed = false,
  onCollapse,
  className,
  width = '256px',
  collapsedWidth = '64px',
  logo,
  footer,
}) => {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleExpanded = (key: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(key)) {
      newExpanded.delete(key);
    } else {
      newExpanded.add(key);
    }
    setExpandedItems(newExpanded);
  };

  const isActive = (item: SidebarItem): boolean => {
    if (item.path) {
      return location.pathname === item.path;
    }
    if (item.children) {
      return item.children.some(child => isActive(child));
    }
    return false;
  };

  const renderSidebarItem = (item: SidebarItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.key);
    const active = isActive(item);

    const itemContent = (
      <div
        className={cn(
          'flex items-center justify-between w-full px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200',
          level > 0 && 'ml-4',
          active
            ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
            : 'text-secondary-700 hover:bg-secondary-100 dark:text-secondary-300 dark:hover:bg-secondary-800',
          item.disabled && 'opacity-50 cursor-not-allowed',
          collapsed && level === 0 && 'justify-center px-2'
        )}
      >
        <div className="flex items-center gap-3">
          {item.icon && (
            <div className={cn('shrink-0', collapsed && level === 0 ? 'w-5 h-5' : 'w-4 h-4')}>
              {item.icon}
            </div>
          )}
          {(!collapsed || level > 0) && (
            <span className="truncate">{item.label}</span>
          )}
        </div>

        <div className="flex items-center gap-2">
          {item.badge && (!collapsed || level > 0) && (
            <span className="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-primary-600 rounded-full">
              {item.badge}
            </span>
          )}
          {hasChildren && (!collapsed || level > 0) && (
            <svg
              className={cn(
                'w-4 h-4 transition-transform duration-200',
                isExpanded ? 'rotate-90' : 'rotate-0'
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          )}
        </div>
      </div>
    );

    const handleClick = () => {
      if (item.disabled) return;
      
      if (hasChildren) {
        toggleExpanded(item.key);
      } else if (item.onClick) {
        item.onClick();
      }
    };

    return (
      <div key={item.key} className="w-full">
        {item.path && !hasChildren ? (
          <Link to={item.path} className="block w-full">
            {itemContent}
          </Link>
        ) : (
          <button
            type="button"
            onClick={handleClick}
            className="block w-full text-left"
            disabled={item.disabled}
          >
            {itemContent}
          </button>
        )}

        {/* Children */}
        {hasChildren && (isExpanded || level > 0) && (!collapsed || level > 0) && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderSidebarItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div
      className={cn(
        'flex flex-col h-full bg-white dark:bg-secondary-900 border-r border-secondary-200 dark:border-secondary-700 transition-all duration-300',
        className
      )}
      style={{ width: collapsed ? collapsedWidth : width }}
    >
      {/* Header */}
      {logo && (
        <div className="flex items-center justify-between p-4 border-b border-secondary-200 dark:border-secondary-700">
          {!collapsed && logo}
          {onCollapse && (
            <button
              type="button"
              onClick={() => onCollapse(!collapsed)}
              className="p-1 rounded-lg hover:bg-secondary-100 dark:hover:bg-secondary-800 transition-colors"
              aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              <svg
                className={cn(
                  'w-5 h-5 transition-transform duration-200',
                  collapsed ? 'rotate-180' : 'rotate-0'
                )}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
              </svg>
            </button>
          )}
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
        {items.map(item => renderSidebarItem(item))}
      </nav>

      {/* Footer */}
      {footer && !collapsed && (
        <div className="p-4 border-t border-secondary-200 dark:border-secondary-700">
          {footer}
        </div>
      )}
    </div>
  );
};
