import React from 'react';
import { Card, CardContent } from '../ui';
import { DashboardMetric } from '../../types';

interface MetricCardProps {
  metric: DashboardMetric;
  className?: string;
}

export const MetricCard: React.FC<MetricCardProps> = ({ metric, className = '' }) => {
  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'success':
        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';
      case 'warning':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20';
      case 'danger':
        return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';
      case 'info':
        return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20';
      default:
        return 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20';
    }
  };

  const getChangeIcon = (changeType?: string) => {
    switch (changeType) {
      case 'increase':
        return '↗';
      case 'decrease':
        return '↘';
      default:
        return '→';
    }
  };

  const getChangeColorClass = (changeType?: string) => {
    switch (changeType) {
      case 'increase':
        return 'text-green-600 dark:text-green-400';
      case 'decrease':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const formatValue = (value: number | string): string => {
    if (typeof value === 'number') {
      // Format large numbers with commas and currency symbol for monetary values
      if (value >= 1000) {
        return `৳${value.toLocaleString()}`;
      }
      return value.toString();
    }
    return value;
  };

  return (
    <Card className={`${className} hover:shadow-lg transition-shadow duration-200`}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
              {metric.label}
            </p>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {formatValue(metric.value)}
            </p>
            {metric.change !== undefined && (
              <div className={`flex items-center mt-2 text-sm ${getChangeColorClass(metric.changeType)}`}>
                <span className="mr-1">{getChangeIcon(metric.changeType)}</span>
                <span>
                  {metric.change > 0 ? '+' : ''}{metric.change}%
                </span>
                <span className="ml-1 text-gray-500 dark:text-gray-400">vs last month</span>
              </div>
            )}
          </div>
          {metric.icon && (
            <div className={`p-3 rounded-full ${getColorClasses(metric.color)}`}>
              <div className="w-6 h-6 flex items-center justify-center">
                {/* Icon placeholder - in a real app, you'd use an icon library */}
                <span className="text-lg font-bold">
                  {metric.icon === 'users' && '👥'}
                  {metric.icon === 'dollar-sign' && '💰'}
                  {metric.icon === 'trending-up' && '📈'}
                  {metric.icon === 'credit-card' && '💳'}
                  {metric.icon === 'building' && '🏢'}
                  {metric.icon === 'clock' && '⏰'}
                  {metric.icon === 'alert-triangle' && '⚠️'}
                  {metric.icon === 'check-circle' && '✅'}
                </span>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default MetricCard;
