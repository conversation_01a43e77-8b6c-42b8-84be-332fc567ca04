import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Loading } from '../ui';
import { QuickActionButton } from './';
import { MemberDashboardData } from '../../types';
import DashboardService from '../../services/dashboardService';

export const MemberDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<MemberDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await DashboardService.getMemberDashboard();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load dashboard data');
        console.error('Error fetching member dashboard:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          ))}
        </div>
        <Loading size="lg" />
      </div>
    );
  }

  if (error || !dashboardData) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 text-lg mb-4">⚠️ {error || 'Failed to load dashboard'}</div>
        <button 
          onClick={() => window.location.reload()} 
          className="px-4 py-2 bg-primary-600 text-white rounded-sm hover:bg-primary-700"
        >
          Retry
        </button>
      </div>
    );
  }

  const { financialSummary, activeLoans, recentPayments, savingsAccounts, quickActions } = dashboardData;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'failed':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400';
      case 'active':
        return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400';
      case 'overdue':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getSavingTypeLabel = (type: string) => {
    switch (type) {
      case 'general':
        return 'General Savings';
      case 'dps':
        return 'DPS Account';
      case 'fdr':
        return 'FDR Account';
      default:
        return 'Savings Account';
    }
  };

  const daysUntilNextPayment = Math.ceil(
    (new Date(financialSummary.nextPaymentDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  );

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      {/* Financial Overview Card */}
      <Card className="bg-linear-to-r from-primary-600 to-primary-700 dark:from-primary-700 dark:to-primary-800 text-white">
        <CardContent className="p-6">
          <h2 className="text-xl font-bold mb-4">Financial Overview</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">৳{financialSummary.savingsBalance.toLocaleString()}</div>
              <div className="text-primary-100 text-sm">Current Savings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">৳{financialSummary.activeLoanAmount.toLocaleString()}</div>
              <div className="text-primary-100 text-sm">Active Loan</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{financialSummary.creditScore}</div>
              <div className="text-primary-100 text-sm">Credit Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">৳{financialSummary.nextPaymentAmount.toLocaleString()}</div>
              <div className="text-primary-100 text-sm">Next Payment</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Next Payment Alert */}
      {daysUntilNextPayment <= 7 && (
        <Card className={`border-l-4 ${daysUntilNextPayment <= 3 ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'}`}>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <span className="text-2xl">{daysUntilNextPayment <= 3 ? '🚨' : '⏰'}</span>
              <div>
                <h3 className={`font-medium ${daysUntilNextPayment <= 3 ? 'text-red-800 dark:text-red-200' : 'text-yellow-800 dark:text-yellow-200'}`}>
                  Payment Due {daysUntilNextPayment <= 0 ? 'Today' : `in ${daysUntilNextPayment} day${daysUntilNextPayment > 1 ? 's' : ''}`}
                </h3>
                <p className={`text-sm ${daysUntilNextPayment <= 3 ? 'text-red-600 dark:text-red-300' : 'text-yellow-600 dark:text-yellow-300'}`}>
                  Next installment of ৳{financialSummary.nextPaymentAmount.toLocaleString()} is due on {formatDate(financialSummary.nextPaymentDate)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>⚡</span>
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <QuickActionButton key={action.id} action={action} size="sm" />
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Active Loans */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>💳</span>
              My Active Loans
            </CardTitle>
          </CardHeader>
          <CardContent>
            {activeLoans.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <span className="text-4xl mb-2 block">💰</span>
                <p>No active loans</p>
                <button className="mt-2 text-primary-600 dark:text-primary-400 hover:underline">
                  Apply for a loan
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {activeLoans.map((loan) => (
                  <div key={loan.id} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          Loan Amount: ৳{loan.loanAmount.toLocaleString()}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Remaining: ৳{loan.remainingAmount.toLocaleString()}
                        </p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(loan.status)}`}>
                        {loan.status}
                      </span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progress</span>
                        <span>{loan.paidInstallments}/{loan.totalInstallments} installments</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-primary-500 h-2 rounded-full"
                          style={{ width: `${(loan.paidInstallments / loan.totalInstallments) * 100}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                        <span>Next Payment: ৳{loan.installmentAmount.toLocaleString()}</span>
                        <span>Due: {formatDate(loan.nextInstallmentDate)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Savings Accounts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>🏦</span>
              My Savings Accounts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {savingsAccounts.map((account) => (
                <div key={account.id} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {getSavingTypeLabel(account.type)}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Interest Rate: {account.interestRate}%
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600 dark:text-green-400">
                        ৳{account.balance.toLocaleString()}
                      </div>
                    </div>
                  </div>
                  {account.monthlyAmount && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Monthly Deposit: ৳{account.monthlyAmount.toLocaleString()}
                    </p>
                  )}
                  {account.maturityDate && (
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Maturity Date: {formatDate(account.maturityDate)}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Payment History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>📊</span>
            Recent Payment History
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recentPayments.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <span className="text-4xl mb-2 block">📝</span>
              <p>No recent payments</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentPayments.map((payment) => (
                <div key={payment.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900/20 flex items-center justify-center">
                      <span className="text-lg">
                        {payment.type === 'installment' && '💳'}
                        {payment.type === 'savings' && '🏦'}
                        {payment.type === 'fee' && '📄'}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {payment.description}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {formatDate(payment.date)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-gray-900 dark:text-white">
                      ৳{payment.amount.toLocaleString()}
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                      {payment.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MemberDashboard;
