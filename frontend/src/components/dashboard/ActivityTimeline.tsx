import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '../ui';
import { ActivityItem } from '../../types';

interface ActivityTimelineProps {
  activities: ActivityItem[];
  title?: string;
  className?: string;
  maxItems?: number;
}

export const ActivityTimeline: React.FC<ActivityTimelineProps> = ({ 
  activities, 
  title = 'Recent Activities',
  className = '',
  maxItems = 10
}) => {
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'loan_application':
        return '📄';
      case 'installment_collection':
        return '💳';
      case 'member_registration':
        return '👤';
      case 'loan_approval':
        return '✅';
      case 'transaction':
        return '💰';
      default:
        return '📋';
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'failed':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const formatAmount = (amount?: number) => {
    if (!amount) return '';
    return `৳${amount.toLocaleString()}`;
  };

  const displayActivities = activities.slice(0, maxItems);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>📊</span>
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {displayActivities.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <span className="text-4xl mb-2 block">📭</span>
            <p>No recent activities</p>
          </div>
        ) : (
          <div className="space-y-4">
            {displayActivities.map((activity, index) => (
              <div key={activity.id} className="flex items-start gap-3">
                {/* Timeline line */}
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900/20 flex items-center justify-center">
                    <span className="text-sm">{getActivityIcon(activity.type)}</span>
                  </div>
                  {index < displayActivities.length - 1 && (
                    <div className="w-px h-6 bg-gray-200 dark:bg-gray-700 mt-2"></div>
                  )}
                </div>
                
                {/* Activity content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        {activity.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {activity.description}
                      </p>
                      {activity.amount && (
                        <p className="text-sm font-medium text-primary-600 dark:text-primary-400 mt-1">
                          {formatAmount(activity.amount)}
                        </p>
                      )}
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatTimestamp(activity.timestamp)}
                        </span>
                        {activity.user && (
                          <>
                            <span className="text-xs text-gray-400">•</span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              by {activity.user}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                    {activity.status && (
                      <span className={`
                        px-2 py-1 rounded-full text-xs font-medium
                        ${getStatusColor(activity.status)}
                      `}>
                        {activity.status}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        
        {activities.length > maxItems && (
          <div className="mt-4 text-center">
            <button className="text-sm text-primary-600 dark:text-primary-400 hover:underline">
              View all activities ({activities.length})
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActivityTimeline;
