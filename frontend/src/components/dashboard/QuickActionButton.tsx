import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../ui';
import { QuickAction } from '../../types';

interface QuickActionButtonProps {
  action: QuickAction;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const QuickActionButton: React.FC<QuickActionButtonProps> = ({
  action,
  className = '',
  size = 'md'
}) => {
  const navigate = useNavigate();
  const getIconElement = (icon: string) => {
    // Icon mapping - in a real app, you'd use an icon library like Lucide React
    const iconMap: Record<string, string> = {
      'user-plus': '👤+',
      'file-text': '📄',
      'credit-card': '💳',
      'calculator': '🧮',
      'check-circle': '✅',
      'trending-up': '📈',
      'users': '👥',
      'building': '🏢',
      'dollar-sign': '💰',
      'bar-chart': '📊',
      'phone': '📞',
      'file': '📁'
    };
    
    return iconMap[icon] || '📋';
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return 'px-3 py-2 text-sm';
      case 'lg':
        return 'px-6 py-4 text-lg';
      default:
        return 'px-4 py-3 text-base';
    }
  };

  const handleClick = () => {
    if (action.onClick) {
      action.onClick();
    } else if (action.href) {
      // Use React Router navigation for internal routes
      if (action.href.startsWith('http') || action.href.startsWith('//')) {
        // External link - use window.location
        window.location.href = action.href;
      } else {
        // Internal route - use React Router
        navigate(action.href);
      }
    }
  };

  return (
    <Button
      variant={action.color === 'primary' ? 'primary' : 'secondary'}
      onClick={handleClick}
      disabled={action.disabled}
      className={`
        ${className}
        ${getSizeClasses(size)}
        flex items-center justify-center gap-2
        min-h-[60px] w-full
        hover:scale-105 transition-transform duration-200
        ${action.disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}
    >
      <span className="text-lg">{getIconElement(action.icon)}</span>
      <span className="font-medium text-center leading-tight">
        {action.label}
      </span>
    </Button>
  );
};

export default QuickActionButton;
