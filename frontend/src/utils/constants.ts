// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    LOGOUT_ALL: '/auth/logout-all',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    UPDATE_PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    SESSIONS: '/auth/sessions',
    REVOKE_SESSION: (sessionId: string) => `/auth/sessions/${sessionId}`,
  },
  USERS: {
    LIST: '/users',
    CREATE: '/users',
    UPDATE: (id: string) => `/users/${id}`,
    DELETE: (id: string) => `/users/${id}`,
    GET: (id: string) => `/users/${id}`,
    STATS: '/users/stats',
    EXPORT: '/users/export',
    BULK_IMPORT: '/users/bulk-import',
    RESET_PASSWORD: (id: string) => `/users/${id}/reset-password`,
    TOGGLE_STATUS: (id: string) => `/users/${id}/toggle-status`,
  },
  BRANCHES: {
    LIST: '/branches',
    CREATE: '/branches',
    UPDATE: (id: string) => `/branches/${id}`,
    DELETE: (id: string) => `/branches/${id}`,
    GET: (id: string) => `/branches/${id}`,
    STATS: (id: string) => `/branches/${id}/stats`,
    MEMBERS: (id: string) => `/branches/${id}/members`,
    FIELD_OFFICERS: (id: string) => `/branches/${id}/field-officers`,
  },
  MEMBERS: {
    LIST: '/members',
    CREATE: '/members',
    UPDATE: (id: string) => `/members/${id}`,
    DELETE: (id: string) => `/members/${id}`,
    GET: (id: string) => `/members/${id}`,
    SEARCH: '/members/search',
    EXPORT: '/members/export',
    UPLOAD_PHOTO: (id: string) => `/members/${id}/photo`,
  },
  LOANS: {
    APPLICATIONS: '/loan-applications',
    CREATE_APPLICATION: '/loan-applications',
    UPDATE_APPLICATION: (id: string) => `/loan-applications/${id}`,
    DELETE_APPLICATION: (id: string) => `/loan-applications/${id}`,
    GET_APPLICATION: (id: string) => `/loan-applications/${id}`,
    APPROVE_APPLICATION: (id: string) => `/loan-applications/${id}/approve`,
    REJECT_APPLICATION: (id: string) => `/loan-applications/${id}/reject`,
    LIST: '/loans',
    GET: (id: string) => `/loans/${id}`,
    INSTALLMENTS: (id: string) => `/loans/${id}/installments`,
  },
  INSTALLMENTS: {
    LIST: '/installments',
    PENDING: '/installments/pending',
    OVERDUE: '/installments/overdue',
    COLLECT: (id: string) => `/installments/${id}/collect`,
    BULK_COLLECT: '/installments/bulk-collect',
    EXPORT: '/installments/export',
    STATS: '/installments/stats',
    SCHEDULE: (loanId: string) => `/installments/schedule/${loanId}`,
  },
  FINANCIAL: {
    TRANSACTIONS: '/transactions',
    CREATE_TRANSACTION: '/transactions',
    REPORTS: '/financial/reports',
    BRANCH_SUMMARY: '/financial/branch-summary',
    CASH_FLOW: '/financial/cash-flow',
    PROFIT_LOSS: '/financial/profit-loss',
    BALANCE_SHEET: '/financial/balance-sheet',
    EXPORT_REPORT: '/financial/export',
  },
  DASHBOARD: {
    ADMIN: '/dashboard/admin',
    MANAGER: '/dashboard/manager',
    FIELD_OFFICER: '/dashboard/field-officer',
    MEMBER: '/dashboard/member',
  },
  UPLOADS: {
    MEMBER_PHOTO: '/uploads/member-photo',
    DOCUMENT: '/uploads/document',
    BULK_IMPORT: '/uploads/bulk-import',
  },
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER: 'user',
  THEME: 'theme',
  LANGUAGE: 'language',
} as const;

// App Configuration
export const APP_CONFIG = {
  NAME: 'Sonali App',
  VERSION: '1.0.0',
  DESCRIPTION: 'A modern full-stack application',
  DEFAULT_PAGE_SIZE: 10,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  SUPPORTED_FILE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: {
    MIN_LENGTH: 8,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  },
  PHONE: /^\+?[\d\s-()]+$/,
} as const;

// UI Constants
export const UI_CONSTANTS = {
  DEBOUNCE_DELAY: 300,
  TOAST_DURATION: 5000,
  MODAL_ANIMATION_DURATION: 200,
  PAGINATION_SIZES: [10, 25, 50, 100],
} as const;
