import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ApiResponse } from '../types';

interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: AxiosError) => boolean;
}

interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  key?: string;
}

interface ApiClientConfig {
  baseURL?: string;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  enableCache?: boolean;
}

class ApiClient {
  private client: AxiosInstance;
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private retryConfig: RetryConfig;

  constructor(config: ApiClientConfig = {}) {
    this.client = axios.create({
      baseURL: config.baseURL || import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
      timeout: config.timeout || 30000, // Increased timeout for file uploads
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.retryConfig = {
      retries: config.retries || 3,
      retryDelay: config.retryDelay || 1000,
      retryCondition: (error: AxiosError) => {
        return !error.response || (error.response.status >= 500 && error.response.status < 600);
      },
    };

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request timestamp for debugging
        config.metadata = { startTime: Date.now() };

        // Log request in development
        if (import.meta.env.DEV) {
          console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
        }

        return config;
      },
      (error) => {
        console.error('❌ Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor with retry logic
    this.client.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // Log response time in development
        if (import.meta.env.DEV && response.config.metadata) {
          const duration = Date.now() - response.config.metadata.startTime;
          console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} (${duration}ms)`);
        }

        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as any;

        // Handle 401 Unauthorized
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Try to refresh token
            const refreshResponse = await this.refreshToken();
            if (refreshResponse) {
              // Retry original request with new token
              originalRequest.headers.Authorization = `Bearer ${refreshResponse.accessToken}`;
              return this.client(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            this.handleAuthFailure();
            return Promise.reject(refreshError);
          }
        }

        // Handle retry logic for server errors
        if (this.shouldRetry(error) && !originalRequest._retryCount) {
          originalRequest._retryCount = 0;
        }

        if (this.shouldRetry(error) && originalRequest._retryCount < this.retryConfig.retries) {
          originalRequest._retryCount++;

          // Calculate delay with exponential backoff
          const delay = this.retryConfig.retryDelay * Math.pow(2, originalRequest._retryCount - 1);

          console.warn(`🔄 Retrying request (${originalRequest._retryCount}/${this.retryConfig.retries}) after ${delay}ms`);

          await this.delay(delay);
          return this.client(originalRequest);
        }

        // Log error in development
        if (import.meta.env.DEV) {
          console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error);
        }

        return Promise.reject(this.normalizeError(error));
      }
    );
  }

  // Helper methods
  private shouldRetry(error: AxiosError): boolean {
    return this.retryConfig.retryCondition ? this.retryConfig.retryCondition(error) : false;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private normalizeError(error: AxiosError): Error {
    const message = error.response?.data?.error || error.message || 'An unexpected error occurred';
    const normalizedError = new Error(message);
    (normalizedError as any).status = error.response?.status;
    (normalizedError as any).code = error.code;
    return normalizedError;
  }

  private async refreshToken(): Promise<{ accessToken: string } | null> {
    try {
      const response = await axios.post(`${this.client.defaults.baseURL}/auth/refresh`, {}, {
        withCredentials: true,
      });

      if (response.data.success && response.data.data?.accessToken) {
        localStorage.setItem('token', response.data.data.accessToken);
        return response.data.data;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private handleAuthFailure(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('sessionId');
    localStorage.removeItem('tokenExpiresAt');

    // Only redirect if not already on login page
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  }

  // Cache methods
  private getCacheKey(url: string, config?: AxiosRequestConfig): string {
    const params = config?.params ? JSON.stringify(config.params) : '';
    return `${url}${params}`;
  }

  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  private setCache<T>(key: string, data: T, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  // Enhanced API methods
  async get<T>(url: string, config?: AxiosRequestConfig & { cache?: CacheConfig }): Promise<ApiResponse<T>> {
    // Check cache first
    if (config?.cache) {
      const cacheKey = config.cache.key || this.getCacheKey(url, config);
      const cached = this.getFromCache<ApiResponse<T>>(cacheKey);
      if (cached) {
        return cached;
      }
    }

    const response = await this.client.get<ApiResponse<T>>(url, config);

    // Cache the response if caching is enabled
    if (config?.cache) {
      const cacheKey = config.cache.key || this.getCacheKey(url, config);
      this.setCache(cacheKey, response.data, config.cache.ttl);
    }

    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  // File upload methods
  async uploadFile<T>(
    url: string,
    file: File,
    options?: {
      onUploadProgress?: (progressEvent: any) => void;
      additionalData?: Record<string, any>;
    }
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    // Add additional data if provided
    if (options?.additionalData) {
      Object.entries(options.additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    const response = await this.client.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: options?.onUploadProgress,
    });

    return response.data;
  }

  async uploadMultipleFiles<T>(
    url: string,
    files: File[],
    options?: {
      onUploadProgress?: (progressEvent: any) => void;
      additionalData?: Record<string, any>;
    }
  ): Promise<ApiResponse<T>> {
    const formData = new FormData();

    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });

    // Add additional data if provided
    if (options?.additionalData) {
      Object.entries(options.additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    const response = await this.client.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: options?.onUploadProgress,
    });

    return response.data;
  }

  // Utility methods
  clearCache(): void {
    this.cache.clear();
  }

  removeCacheEntry(key: string): void {
    this.cache.delete(key);
  }

  isOnline(): boolean {
    return navigator.onLine;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.client.get('/health', { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }
}

export const apiClient = new ApiClient();
