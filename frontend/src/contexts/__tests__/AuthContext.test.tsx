import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider, useAuth } from '../AuthContext';
import { UserRole } from '../../types';

// Mock the auth service
jest.mock('../../services/authService', () => ({
  authService: {
    isAuthenticated: jest.fn(),
    getStoredUser: jest.fn(),
    getStoredToken: jest.fn(),
    login: jest.fn(),
    logout: jest.fn(),
    getProfile: jest.fn(),
  },
}));

const mockAuthService = require('../../services/authService').authService;

// Test component that uses the auth context
const TestComponent: React.FC = () => {
  const { user, isAuthenticated, login, logout, hasRole, hasPermission } = useAuth();

  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'authenticated' : 'not-authenticated'}
      </div>
      <div data-testid="user-name">{user?.name || 'no-user'}</div>
      <div data-testid="user-role">{user?.role || 'no-role'}</div>
      <div data-testid="has-admin-role">
        {hasRole(UserRole.ADMIN) ? 'has-admin' : 'no-admin'}
      </div>
      <div data-testid="has-user-create-permission">
        {hasPermission('user.create') ? 'can-create-user' : 'cannot-create-user'}
      </div>
      <button onClick={() => login({ identifier: 'test', password: 'test' })}>
        Login
      </button>
      <button onClick={logout}>Logout</button>
    </div>
  );
};

const renderWithAuthProvider = (component: React.ReactElement) => {
  return render(<AuthProvider>{component}</AuthProvider>);
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('provides initial unauthenticated state', () => {
    mockAuthService.isAuthenticated.mockReturnValue(false);
    mockAuthService.getStoredUser.mockReturnValue(null);
    mockAuthService.getStoredToken.mockReturnValue(null);

    renderWithAuthProvider(<TestComponent />);

    expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
    expect(screen.getByTestId('user-name')).toHaveTextContent('no-user');
    expect(screen.getByTestId('user-role')).toHaveTextContent('no-role');
  });

  it('provides authenticated state when user is logged in', async () => {
    const mockUser = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      isActive: true,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    };

    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getStoredUser.mockReturnValue(mockUser);
    mockAuthService.getStoredToken.mockReturnValue('mock-token');

    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      expect(screen.getByTestId('user-name')).toHaveTextContent('John Doe');
      expect(screen.getByTestId('user-role')).toHaveTextContent('admin');
    });
  });

  it('handles login correctly', async () => {
    const user = userEvent.setup();
    const mockUser = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      isActive: true,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    };

    const mockLoginResult = {
      user: mockUser,
      accessToken: 'mock-token',
      sessionId: 'mock-session',
      expiresAt: '2023-12-31',
    };

    mockAuthService.isAuthenticated.mockReturnValue(false);
    mockAuthService.login.mockResolvedValue(mockLoginResult);

    renderWithAuthProvider(<TestComponent />);

    const loginButton = screen.getByText('Login');
    await user.click(loginButton);

    await waitFor(() => {
      expect(mockAuthService.login).toHaveBeenCalledWith({
        identifier: 'test',
        password: 'test',
      });
    });
  });

  it('handles logout correctly', async () => {
    const user = userEvent.setup();
    const mockUser = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      isActive: true,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    };

    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getStoredUser.mockReturnValue(mockUser);
    mockAuthService.logout.mockResolvedValue(undefined);

    renderWithAuthProvider(<TestComponent />);

    const logoutButton = screen.getByText('Logout');
    await user.click(logoutButton);

    await waitFor(() => {
      expect(mockAuthService.logout).toHaveBeenCalled();
    });
  });

  it('checks roles correctly', async () => {
    const mockUser = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      isActive: true,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    };

    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getStoredUser.mockReturnValue(mockUser);

    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('has-admin-role')).toHaveTextContent('has-admin');
    });
  });

  it('checks permissions correctly', async () => {
    const mockUser = {
      id: '1',
      name: 'John Doe',
      email: '<EMAIL>',
      role: UserRole.ADMIN,
      isActive: true,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    };

    mockAuthService.isAuthenticated.mockReturnValue(true);
    mockAuthService.getStoredUser.mockReturnValue(mockUser);

    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(screen.getByTestId('has-user-create-permission')).toHaveTextContent('can-create-user');
    });
  });

  it('handles expired token correctly', async () => {
    mockAuthService.isAuthenticated.mockReturnValue(false);
    mockAuthService.getStoredUser.mockReturnValue({ name: 'John' });
    mockAuthService.getStoredToken.mockReturnValue('expired-token');
    mockAuthService.logout.mockResolvedValue(undefined);

    renderWithAuthProvider(<TestComponent />);

    await waitFor(() => {
      expect(mockAuthService.logout).toHaveBeenCalled();
      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
    });
  });

  it('throws error when useAuth is used outside AuthProvider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAuth must be used within an AuthProvider');

    consoleSpy.mockRestore();
  });
});
