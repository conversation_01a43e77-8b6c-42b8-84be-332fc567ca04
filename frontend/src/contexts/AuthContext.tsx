import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authService } from '../services/authService';
import { User, LoginCredentials, UserRole } from '../types';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<{ user: User; accessToken: string; sessionId: string; expiresAt: string }>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  hasRole: (roles: UserRole | UserRole[]) => boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && authService.isAuthenticated();

  // Role-based permissions mapping
  const rolePermissions: Record<UserRole, string[]> = {
    [UserRole.ADMIN]: [
      'user.create', 'user.read', 'user.update', 'user.delete',
      'member.create', 'member.read', 'member.update', 'member.delete',
      'loan.create', 'loan.read', 'loan.update', 'loan.delete', 'loan.approve',
      'branch.create', 'branch.read', 'branch.update', 'branch.delete',
      'report.read', 'report.export',
      'system.settings', 'advertisement.manage'
    ],
    [UserRole.MANAGER]: [
      'member.create', 'member.read', 'member.update',
      'loan.create', 'loan.read', 'loan.update', 'loan.approve',
      'installment.read', 'installment.update',
      'report.read', 'report.export',
      'branch.read'
    ],
    [UserRole.FIELD_OFFICER]: [
      'member.create', 'member.read', 'member.update',
      'loan.create', 'loan.read', 'loan.update',
      'installment.read', 'installment.update', 'installment.collect',
      'saving.read', 'saving.update',
      'report.read'
    ],
    [UserRole.MEMBER]: [
      'profile.read', 'profile.update',
      'loan.read', 'loan.apply',
      'installment.read',
      'saving.read'
    ]
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedUser = authService.getStoredUser();
        const token = authService.getStoredToken();

        if (storedUser && token) {
          // Verify token is still valid
          if (authService.isAuthenticated()) {
            setUser(storedUser);
          } else {
            // Token expired, clear stored data
            await authService.logout();
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        await authService.logout();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials) => {
    setIsLoading(true);
    try {
      const result = await authService.login(credentials);
      setUser(result.user);
      return result;
    } catch (error) {
      setUser(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await authService.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      // Clear local state even if API call fails
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAuth = async () => {
    try {
      const profile = await authService.getProfile();
      setUser(profile);
    } catch (error) {
      console.error('Refresh auth error:', error);
      await logout();
    }
  };

  const hasRole = (roles: UserRole | UserRole[]): boolean => {
    if (!user) return false;
    
    const roleArray = Array.isArray(roles) ? roles : [roles];
    return roleArray.includes(user.role);
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    const userPermissions = rolePermissions[user.role] || [];
    return userPermissions.includes(permission);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    refreshAuth,
    hasRole,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
