import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const isProduction = mode === 'production'

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
      },
    },

    build: {
      // Production optimizations
      minify: isProduction ? 'terser' : false,
      terserOptions: isProduction ? {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.warn'],
        },
        mangle: {
          toplevel: true,
          safari10: true,
        },
        format: {
          comments: false,
        },
      } : {},
      rollupOptions: {
        output: {
          // Obfuscate chunk names in production
          chunkFileNames: isProduction
            ? 'assets/[hash].js'
            : 'assets/[name]-[hash].js',
          entryFileNames: isProduction
            ? 'assets/[hash].js'
            : 'assets/[name]-[hash].js',
          assetFileNames: isProduction
            ? 'assets/[hash].[ext]'
            : 'assets/[name]-[hash].[ext]',
          manualChunks: isProduction ? {
            // Split vendor chunks for better caching
            vendor: ['react', 'react-dom'],
            router: ['react-router-dom'],
            ui: ['@tanstack/react-query'],
          } : undefined,
        },
      },
      // Source maps only in development
      sourcemap: !isProduction,
      // Optimize chunk size
      chunkSizeWarningLimit: 1000,
    },
    server: {
      port: 5173,
      host: true,
      cors: true,
    },
    preview: {
      port: 4173,
      host: true,
    },
    // Environment variables
    envPrefix: 'VITE_',
  }
})
