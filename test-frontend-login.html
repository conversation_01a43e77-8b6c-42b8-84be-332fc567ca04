<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 5px; }
        input { padding: 10px; width: 300px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
    </style>
</head>
<body>
    <h1>Test Login</h1>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="identifier">Member ID or Email:</label>
            <input type="text" id="identifier" value="ADMIN001" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="admin123" required>
        </div>
        
        <button type="submit">Login</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const identifier = document.getElementById('identifier').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result';
                resultDiv.innerHTML = 'Logging in...';
                
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        identifier: identifier,
                        password: password,
                        rememberMe: false
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ Login Successful!</h3>
                        <p><strong>User:</strong> ${data.user ? data.user.name : (data.data ? data.data.user.name : 'Unknown')}</p>
                        <p><strong>Role:</strong> ${data.user ? data.user.role : (data.data ? data.data.user.role : 'Unknown')}</p>
                        <p><strong>Member ID:</strong> ${data.user ? data.user.memberId : (data.data ? data.data.user.memberId : 'Unknown')}</p>
                        <p><strong>Token:</strong> ${data.accessToken ? data.accessToken.substring(0, 20) + '...' : (data.data ? data.data.accessToken.substring(0, 20) + '...' : 'No token')}</p>
                        <details>
                            <summary>Full Response</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>❌ Login Failed</h3>
                        <p><strong>Error:</strong> ${data.error || data.message || 'Unknown error'}</p>
                        <details>
                            <summary>Full Response</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ Network Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        });
        
        // Test all credentials
        const testAllCredentials = async () => {
            const credentials = [
                { identifier: 'ADMIN001', password: 'admin123', name: 'Korim (Admin)' },
                { identifier: 'MGR001', password: 'manager123', name: 'Rajib (Manager)' },
                { identifier: 'FO001', password: 'officer123', name: 'Hamid (Field Officer)' },
                { identifier: 'MEM001', password: 'member123', name: 'Alim (Member)' }
            ];
            
            console.log('Testing all credentials...');
            
            for (const cred of credentials) {
                try {
                    const response = await fetch('http://localhost:3001/api/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            identifier: cred.identifier,
                            password: cred.password,
                            rememberMe: false
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        console.log(`✅ ${cred.name}: Login successful`);
                    } else {
                        console.log(`❌ ${cred.name}: Login failed - ${data.error || data.message}`);
                    }
                } catch (error) {
                    console.log(`❌ ${cred.name}: Network error - ${error.message}`);
                }
            }
        };
        
        // Add test button
        const testButton = document.createElement('button');
        testButton.textContent = 'Test All Credentials';
        testButton.style.marginLeft = '10px';
        testButton.onclick = testAllCredentials;
        document.querySelector('button').parentNode.appendChild(testButton);
    </script>
</body>
</html>
