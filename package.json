{"name": "sonali-app", "version": "1.0.0", "description": "A modern full-stack web application built with React 19, TypeScript, Node.js, Express, and MySQL", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && npm run preview", "start:backend": "cd backend && npm start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "clean": "rm -rf frontend/node_modules backend/node_modules frontend/dist backend/dist", "deploy": "npm run build && pm2 restart ecosystem.config.js", "deploy:production": "npm run build && pm2 restart ecosystem.config.js --env production", "setup": "npm run install:all && npm run build", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:logs": "pm2 logs", "db:migrate": "cd backend && npx prisma migrate deploy", "db:seed": "cd backend && npx prisma db seed"}, "keywords": ["react", "typescript", "nodejs", "express", "mysql", "sequelize", "fullstack", "vite", "tailwindcss"], "author": "Sonali App Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "workspaces": ["frontend", "backend", "shared/types"], "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.2"}}