<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 200px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Frontend API Test</h1>
    
    <div class="test-section">
        <h3>Environment Detection</h3>
        <div id="env-info"></div>
    </div>
    
    <div class="test-section">
        <h3>API Base URL Test</h3>
        <div id="api-url-info"></div>
        <button onclick="testApiUrls()">Test API URLs</button>
        <div id="api-url-results"></div>
    </div>
    
    <div class="test-section">
        <h3>Login Test</h3>
        <div>
            <input type="text" id="identifier" placeholder="Member ID" value="ADMIN001">
            <input type="password" id="password" placeholder="Password" value="admin123">
            <button onclick="testLogin()">Test Login</button>
        </div>
        <div id="login-results"></div>
    </div>
    
    <script>
        // Environment detection
        function detectEnvironment() {
            const envInfo = document.getElementById('env-info');
            const info = {
                'Current URL': window.location.href,
                'User Agent': navigator.userAgent,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'Port': window.location.port || 'default'
            };
            
            let html = '<div class="info">';
            for (const [key, value] of Object.entries(info)) {
                html += `<strong>${key}:</strong> ${value}<br>`;
            }
            html += '</div>';
            envInfo.innerHTML = html;
        }
        
        // Test different API URLs
        async function testApiUrls() {
            const resultsDiv = document.getElementById('api-url-results');
            resultsDiv.innerHTML = '<div class="info">Testing API URLs...</div>';
            
            const apiUrls = [
                'http://localhost:3001/api',
                'http://127.0.0.1:3001/api',
                'http://**************:3001/api'
            ];
            
            let results = '';
            
            for (const url of apiUrls) {
                try {
                    const response = await fetch(`${url}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            identifier: 'ADMIN001',
                            password: 'admin123'
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        results += `<div class="success"><strong>${url}:</strong> ✅ Working</div>`;
                    } else {
                        results += `<div class="error"><strong>${url}:</strong> ❌ Failed - ${data.error || data.message}</div>`;
                    }
                } catch (error) {
                    results += `<div class="error"><strong>${url}:</strong> ❌ Network Error - ${error.message}</div>`;
                }
            }
            
            resultsDiv.innerHTML = results;
        }
        
        // Test login with current configuration
        async function testLogin() {
            const identifier = document.getElementById('identifier').value;
            const password = document.getElementById('password').value;
            const resultsDiv = document.getElementById('login-results');
            
            resultsDiv.innerHTML = '<div class="info">Testing login...</div>';
            
            // Simulate frontend API client behavior
            const apiUrls = [
                'http://localhost:3001/api',
                'http://127.0.0.1:3001/api',
                'http://**************:3001/api'
            ];
            
            let results = '<h4>Login Test Results:</h4>';
            
            for (const baseUrl of apiUrls) {
                try {
                    console.log(`Testing login with ${baseUrl}...`);
                    
                    const response = await fetch(`${baseUrl}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            identifier: identifier,
                            password: password,
                            rememberMe: false
                        })
                    });
                    
                    const data = await response.json();
                    
                    console.log(`Response from ${baseUrl}:`, data);
                    
                    if (response.ok && data.success) {
                        results += `<div class="success">
                            <strong>${baseUrl}:</strong> ✅ Login Successful<br>
                            User: ${data.user?.name || data.data?.user?.name}<br>
                            Role: ${data.user?.role || data.data?.user?.role}<br>
                            Token: ${(data.accessToken || data.data?.accessToken)?.substring(0, 20)}...
                        </div>`;
                    } else {
                        results += `<div class="error">
                            <strong>${baseUrl}:</strong> ❌ Login Failed<br>
                            Error: ${data.error || data.message || 'Unknown error'}<br>
                            Status: ${response.status}
                        </div>`;
                    }
                    
                    // Show full response for debugging
                    results += `<details><summary>Full Response</summary><pre>${JSON.stringify(data, null, 2)}</pre></details>`;
                    
                } catch (error) {
                    console.error(`Error with ${baseUrl}:`, error);
                    results += `<div class="error">
                        <strong>${baseUrl}:</strong> ❌ Network Error<br>
                        Error: ${error.message}
                    </div>`;
                }
            }
            
            resultsDiv.innerHTML = results;
        }
        
        // Show API URL info
        function showApiUrlInfo() {
            const infoDiv = document.getElementById('api-url-info');
            
            // Try to detect what the frontend would use
            const possibleUrls = [
                'http://localhost:3001/api (Development default)',
                'http://127.0.0.1:3001/api (Localhost alternative)',
                'http://**************:3001/api (VPS IP)',
                'https://www.sonalibd.org/api (Production)'
            ];
            
            let html = '<div class="info"><strong>Possible API URLs:</strong><br>';
            possibleUrls.forEach(url => {
                html += `• ${url}<br>`;
            });
            html += '</div>';
            
            infoDiv.innerHTML = html;
        }
        
        // Initialize
        detectEnvironment();
        showApiUrlInfo();
    </script>
</body>
</html>
