#!/bin/bash

# Sonali App Complete Installation Script (Native - No Docker)
# This script installs and sets up the complete Sonali App on a VPS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="sonali-app"
APP_DIR="/var/www/$APP_NAME"
DOMAIN="www.sonalibd.org"
DB_NAME="sonali_app_production"
DB_USER="sonali_user"
DB_PASSWORD="fe97f51e2647814ee59a3673dff44552ebddb38be6f03589b005eea7ebc6c6b8"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "Please run this script as root (use sudo)"
    exit 1
fi

print_header "🚀 Starting Sonali App Complete Installation"
print_status "Installing on: $(hostname -I | awk '{print $1}')"
print_status "Domain: $DOMAIN"
print_status "App Directory: $APP_DIR"

# Update system
print_status "Updating system packages..."
apt update && apt upgrade -y

# Install essential packages
print_status "Installing essential packages..."
apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release build-essential

# Install Node.js 20 LTS
print_status "Installing Node.js 20 LTS..."
curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
apt-get install -y nodejs

# Verify Node.js installation
node_version=$(node --version)
npm_version=$(npm --version)
print_status "Node.js version: $node_version"
print_status "NPM version: $npm_version"

# Install PM2 globally
print_status "Installing PM2 process manager..."
npm install -g pm2

# Install MySQL 8.0
print_status "Installing MySQL 8.0..."
apt install -y mysql-server mysql-client

# Start MySQL service
systemctl start mysql
systemctl enable mysql

# Install Nginx
print_status "Installing Nginx..."
apt install -y nginx

# Install Certbot for SSL
print_status "Installing Certbot for SSL certificates..."
apt install -y certbot python3-certbot-nginx

# Configure firewall
print_status "Configuring UFW firewall..."
ufw allow OpenSSH
ufw allow 'Nginx Full'
ufw allow 80
ufw allow 443
ufw --force enable

# Create application directory
print_status "Creating application directory..."
mkdir -p $APP_DIR
mkdir -p $APP_DIR/logs
mkdir -p $APP_DIR/uploads
mkdir -p $APP_DIR/backups

# Clone the repository (you'll need to replace this with your actual repo)
print_status "Cloning Sonali App repository..."
cd $APP_DIR
# git clone https://github.com/your-username/sonali-app.git .
# For now, we'll assume the code is already there

# Set proper permissions
print_status "Setting proper permissions..."
chown -R www-data:www-data $APP_DIR
chmod -R 755 $APP_DIR

# Setup MySQL database
print_status "Setting up MySQL database..."
mysql -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;"
mysql -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
mysql -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

# Install application dependencies
if [ -f "$APP_DIR/package.json" ]; then
    print_status "Installing application dependencies..."
    cd $APP_DIR
    npm run install:all
    
    # Copy environment file
    print_status "Setting up environment configuration..."
    cp deploy/.env.production backend/.env
    
    # Build the application
    print_status "Building the application..."
    npm run build
    
    # Start with PM2
    print_status "Starting application with PM2..."
    pm2 start ecosystem.config.js --env production
    pm2 save
    pm2 startup
else
    print_warning "Application code not found. Please clone your repository to $APP_DIR"
fi

# Configure Nginx
print_status "Configuring Nginx..."
# The nginx configuration should already be in place from the existing setup

# Start and enable services
print_status "Starting and enabling services..."
systemctl start nginx
systemctl enable nginx
systemctl restart nginx

# Setup SSL with Certbot
print_status "Setting up SSL certificates..."
print_warning "SSL setup requires manual intervention. Run the following command after installation:"
print_warning "certbot --nginx -d $DOMAIN"

print_success "✅ Sonali App installation completed successfully!"
print_status ""
print_status "📝 Next steps:"
print_status "1. Run: certbot --nginx -d $DOMAIN (to set up SSL)"
print_status "2. Verify the application is running: pm2 list"
print_status "3. Check logs: pm2 logs"
print_status "4. Access your application: https://$DOMAIN"
print_status ""
print_status "🔧 Installed components:"
print_status "- Node.js $node_version"
print_status "- NPM $npm_version"
print_status "- PM2 (process manager)"
print_status "- MySQL 8.0"
print_status "- Nginx"
print_status "- Certbot (SSL certificates)"
print_status "- UFW Firewall (configured)"
print_status ""
print_status "🌐 Application URLs:"
print_status "- Frontend: https://$DOMAIN"
print_status "- API: https://$DOMAIN/api"
print_status "- Health Check: https://$DOMAIN/health"
print_status ""
print_status "🎉 Your Sonali App is ready!"
